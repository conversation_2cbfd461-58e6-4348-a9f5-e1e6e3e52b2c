﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/booking_entity.dart';
import '../../repositories/booking_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for creating a new booking
class CreateBooking implements UseCase<BookingEntity, CreateBookingParams> {
  final BookingRepository repository;

  CreateBooking(this.repository);

  @override
  Future<Either<Failure, BookingEntity>> call(CreateBookingParams params) async {
    return await repository.createBooking(
      hotelId: params.hotelId,
      roomId: params.roomId,
      checkInDate: params.checkInDate,
      checkOutDate: params.checkOutDate,
      numberOfGuests: params.numberOfGuests,
      numberOfRooms: params.numberOfRooms,
      guestDetails: params.guestDetails,
      specialRequests: params.specialRequests,
    );
  }
}

/// Parameters for CreateBooking use case
class CreateBookingParams extends Equatable {
  final String hotelId;
  final String roomId;
  final DateTime checkInDate;
  final DateTime checkOutDate;
  final int numberOfGuests;
  final int numberOfRooms;
  final GuestDetailsEntity guestDetails;
  final String? specialRequests;

  const CreateBookingParams({
    required this.hotelId,
    required this.roomId,
    required this.checkInDate,
    required this.checkOutDate,
    required this.numberOfGuests,
    required this.numberOfRooms,
    required this.guestDetails,
    this.specialRequests,
  });

  @override
  List<Object?> get props => [
    hotelId,
    roomId,
    checkInDate,
    checkOutDate,
    numberOfGuests,
    numberOfRooms,
    guestDetails,
    specialRequests,
  ];
}

