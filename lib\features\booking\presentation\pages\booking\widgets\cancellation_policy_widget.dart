import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';

class CancellationPolicyWidget extends StatefulWidget {
  final String checkInDate;
  final String checkInTime;

  const CancellationPolicyWidget({
    Key? key,
    required this.checkInDate,
    required this.checkInTime,
  }) : super(key: key);

  @override
  State<CancellationPolicyWidget> createState() => _CancellationPolicyWidgetState();
}

class _CancellationPolicyWidgetState extends State<CancellationPolicyWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Cancellation Policy',
            style: AppTextStyles.headline3.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          // Description
          Text(
            '100% amount will be deducted on cancellation.',
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 24),

          // Timeline
          _buildCancellationTimeline(context),

          const SizedBox(height: 16),

          // View policy button
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Row(
              children: [
                Text(
                  'View Cancellation Policy',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: AppColors.primary,
                  size: 18,
                ),
              ],
            ),
          ),

          // Expanded policy details
          if (_isExpanded) ...[
            const SizedBox(height: 16),
            _buildExpandedCancellationPolicy(),
          ],
        ],
      ),
    );
  }

  Widget _buildExpandedCancellationPolicy() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        const SizedBox(height: 16),

        // Fully refundable
        _buildTimelineCard(
          'Fully refundable before 01 Apr 2025 11:59 PM',
          'Cancel your reservation before 01 Apr 2025 11:59 PM, to get a full refund',
          Colors.green.shade700,
        ),
        const SizedBox(height: 16),

        // Partially refundable
        _buildTimelineCard(
          '50% refundable between 02 Apr and 04 Apr 2025',
          'Cancel between 02 Apr 2025 12:00 AM and 04 Apr 2025 11:59 PM, to get a 50% refund',
          Colors.orange,
        ),
        const SizedBox(height: 16),

        // Non-refundable
        _buildTimelineCard(
          'Non-refundable after 01 May 2025 11:59 PM',
          '100% amount will be deducted on cancellation after 01 May 2025 11:59 PM',
          Colors.red,
        ),
        const SizedBox(height: 16),

        // Note
        const Divider(),
        const SizedBox(height: 16),
        const Text(
          'Note:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Date and time is displayed as per the local time zone of your hotel\'s location.',
          style: TextStyle(
            color: Colors.grey.shade700,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildCancellationTimeline(BuildContext context) {
    return Row(
      children: [
        // Fully refundable
        Expanded(
          child: _buildTimelineSegment(
            'Fully\nRefundable',
            Colors.green.shade700,
            isFirst: true,
          ),
        ),

        // Partially refundable
        Expanded(
          child: _buildTimelineSegment(
            'Partially\nRefundable',
            Colors.orange,
          ),
        ),

        // Non-refundable
        Expanded(
          child: _buildTimelineSegment(
            'Non\nRefundable',
            Colors.red,
            isLast: true,
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineSegment(String text, Color color, {bool isFirst = false, bool isLast = false}) {
    return Column(
      children: [
        // Date marker
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
        ),
        const SizedBox(height: 4),

        // Label
        Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineCard(String title, String description, Color color) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 13,
            ),
          ),
        ],
      ),
    );
  }
}
