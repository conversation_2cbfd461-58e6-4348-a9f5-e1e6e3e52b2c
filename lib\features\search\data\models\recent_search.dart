﻿import 'dart:convert';
import 'package:kind_ali/features/home/<USER>/providers/home_notifier.dart';

class RecentSearch {
  final String destination;
  final DateTime checkInDate;
  final DateTime checkOutDate;
  final List<RoomData> rooms;
  final DateTime searchTimestamp;

  RecentSearch({
    required this.destination,
    required this.checkInDate,
    required this.checkOutDate,
    required this.rooms,
    required this.searchTimestamp,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'destination': destination,
      'checkInDate': checkInDate.toIso8601String(),
      'checkOutDate': checkOutDate.toIso8601String(),
      'rooms': rooms.map((room) => room.toJson()).toList(),
      'searchTimestamp': searchTimestamp.toIso8601String(),
    };
  }

  // Create from JSON
  factory RecentSearch.fromJson(Map<String, dynamic> json) {
    return RecentSearch(
      destination: json['destination'] ?? '',
      checkInDate: DateTime.parse(json['checkInDate']),
      checkOutDate: DateTime.parse(json['checkOutDate']),
      rooms: (json['rooms'] as List<dynamic>)
          .map((roomJson) => RoomData.fromJson(roomJson))
          .toList(),
      searchTimestamp: DateTime.parse(json['searchTimestamp']),
    );
  }

  // Get total number of guests
  int get totalGuests {
    int total = 0;
    for (var room in rooms) {
      total += room.adults + room.children;
    }
    return total;
  }

  // Get total number of adults
  int get totalAdults {
    int total = 0;
    for (var room in rooms) {
      total += room.adults;
    }
    return total;
  }

  // Get total number of children
  int get totalChildren {
    int total = 0;
    for (var room in rooms) {
      total += room.children;
    }
    return total;
  }

  // Get number of nights
  int get numberOfNights {
    return checkOutDate.difference(checkInDate).inDays;
  }

  // Get formatted guest summary
  String get guestSummary {
    if (totalChildren > 0) {
      return '$totalAdults Adults, $totalChildren Children · ${rooms.length} Room${rooms.length > 1 ? 's' : ''}';
    } else {
      return '$totalAdults Adult${totalAdults > 1 ? 's' : ''} · ${rooms.length} Room${rooms.length > 1 ? 's' : ''}';
    }
  }

  // Get formatted date range
  String get dateRange {
    final checkIn = '${checkInDate.day}/${checkInDate.month}';
    final checkOut = '${checkOutDate.day}/${checkOutDate.month}';
    return '$checkIn - $checkOut';
  }
}


