import 'package:flutter/material.dart';

class DestinationSection extends StatelessWidget {
 final String name; final String imageUrl;
  const DestinationSection({super.key, required this.name, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
      return Container(
      width: 160, // Slightly wider for more presence
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20), // More rounded corners
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withAlpha(20),
        //     blurRadius: 12,
        //     spreadRadius: 2,
        //     offset: Offset(0, 6),
        //   ),
        // ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Destination image
            Image.network(
              imageUrl,
              fit: BoxFit.cover,
            ),

            // Luxury gradient overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withAlpha(180),
                  ],
                  stops: [
                    0.6,
                    1.0
                  ], // Grad<PERSON> starts lower for a more premium look
                ),
              ),
            ),

            // Gold accent line at the bottom for luxury feel
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 3,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Colors.amber.shade200,
                      Colors.amber.shade600,
                      Colors.amber.shade200,
                    ],
                  ),
                ),
              ),
            ),

            // Destination name with improved typography
            Positioned(
              bottom: 14,
              left: 14,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      letterSpacing:
                          0.5, // Improved letter spacing for luxury feel
                      shadows: [
                        Shadow(
                          color: Colors.black.withAlpha(150),
                          blurRadius: 3,
                          offset: Offset(1, 1),
                        ),
                      ],
                    ),
                  ),
                  // Small indicator line for visual interest
                  Container(
                    margin: EdgeInsets.only(top: 4),
                    width: 30,
                    height: 2,
                    decoration: BoxDecoration(
                      color: Colors.amber.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
