import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_details.dart';
import 'package:kind_ali/features/wishlist/presentation/providers/wishlist_notifier.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:url_launcher/url_launcher.dart';

class HotelCard extends ConsumerStatefulWidget {
  final InventoryInfoList hotel;
  final VoidCallback onTap;
  final List<String> imageUrls;

  const HotelCard({
    Key? key,
    required this.hotel,
    required this.onTap,
    this.imageUrls = const [],
  }) : super(key: key);

  @override
  ConsumerState<HotelCard> createState() => _HotelCardState();
}

class _HotelCardState extends ConsumerState<HotelCard> {

  // Method to show share dialog
  void _showShareDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 8,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  spreadRadius: 5,
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                Text(
                  'hotel.detail.share'.tr,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Share ${widget.hotel.name ?? 'this hotel'} with friends',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textLight,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Share options
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // WhatsApp option
                    _buildShareOption(
                      icon: Icons.chat,
                      label: 'WhatsApp',
                      color: const Color(0xFF25D366),
                      onTap: () {
                        Navigator.of(context).pop();
                        _shareViaWhatsApp();
                      },
                    ),

                    // Email option
                    _buildShareOption(
                      icon: Icons.email,
                      label: 'Email',
                      color: AppColors.primary,
                      onTap: () {
                        Navigator.of(context).pop();
                        _shareViaEmail();
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Cancel button
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: AppColors.textLight,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShareOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Method to share via WhatsApp
  void _shareViaWhatsApp() async {
    final String hotelName = widget.hotel.name ?? 'Amazing Hotel';
    final String location = "${widget.hotel.locality ?? 'Goa'}, ${widget.hotel.city ?? ''}";
    final String price = '₹${widget.hotel.fareDetail?.totalPrice ?? 0}';
    final String rating = widget.hotel.starRating?.toStringAsFixed(1) ?? '4.5';

    // Create a shareable link (you can replace this with your actual deep link)
    final String hotelLink = 'https://yourapp.com/hotel/${widget.hotel.hotelId ?? ''}';

    final String message = '''
🏨 *$hotelName*

📍 Location: $location
⭐ Rating: $rating/5
💰 Price: $price per night

Check out this amazing hotel I found!

$hotelLink

Book now for the best deals! 🎉
''';

    final String whatsappUrl = 'whatsapp://send?text=${Uri.encodeComponent(message)}';

    try {
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(Uri.parse(whatsappUrl));
      } else {
        // If WhatsApp is not installed, show a message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('WhatsApp is not installed on this device'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open WhatsApp'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Method to share via Email
  void _shareViaEmail() async {
    final String hotelName = widget.hotel.name ?? 'Amazing Hotel';
    final String location = "${widget.hotel.locality ?? 'Goa'}, ${widget.hotel.city ?? ''}";
    final String price = '₹${widget.hotel.fareDetail?.totalPrice ?? 0}';
    final String rating = widget.hotel.starRating?.toStringAsFixed(1) ?? '4.5';

    // Create a shareable link (you can replace this with your actual deep link)
    final String hotelLink = 'https://yourapp.com/hotel/${widget.hotel.hotelId ?? ''}';

    final String subject = 'Check out this amazing hotel: $hotelName';
    final String body = '''
Hi there!

I found this amazing hotel and thought you might be interested:

Hotel: $hotelName
Location: $location
Rating: $rating out of 5 stars
Price: $price per night

$hotelLink

Hope you find it useful!

Best regards
''';

    final String emailUrl = 'mailto:?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}';

    try {
      if (await canLaunchUrl(Uri.parse(emailUrl))) {
        await launchUrl(Uri.parse(emailUrl));
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No email app found on this device'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open email app'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
// Use hotel images from JSON if available, otherwise use placeholder images
    final List<String> imagesToDisplay = widget.hotel.imageInfoList != null &&
            widget.hotel.imageInfoList!.isNotEmpty
        ? [widget.hotel.imageInfoList!.first.url!]
        : AppImages.allImages.toList();

// Fixed dimensions for card
    final double baseCardHeight =
        460.0; // Increased by 10px to accommodate overflow
    final double cardHeight = baseCardHeight;
    final double detailsHeight = 300.0; // Increased by 10px
    final double borderRadius = AppDimensions.radiusExtraLarge;

// Get room details for the first room if available
    final RoomDetails? firstRoom =
        widget.hotel.roomDetails != null && widget.hotel.roomDetails!.isNotEmpty
            ? widget.hotel.roomDetails![0]
            : null;

// Feature list items based on hotel and room data
    final List<FeatureItem> features = [
      FeatureItem(Icons.apartment_outlined, 'Private suite'),
      FeatureItem(Icons.square_foot_outlined, '41m²'),
      FeatureItem(Icons.people_outline, '${firstRoom?.bed ?? 4} guests'),
      FeatureItem(Icons.king_bed_outlined, '2 beds'),
      FeatureItem(Icons.bed_outlined, '2 bedrooms'),
      FeatureItem(Icons.bathtub_outlined, '1 bathroom'),
    ];

// Split features into two rows
    final int halfLength = (features.length / 2).ceil();
    final List<FeatureItem> firstRowFeatures = features.sublist(0, halfLength);
    final List<FeatureItem> secondRowFeatures = features.sublist(halfLength);

    return InkWell(
      onTap: widget.onTap,
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingSizeSmall),
        child: Container(
          decoration: BoxDecoration(
              border: Border.all(color: AppColors.neutralLight),
              borderRadius: BorderRadius.circular(22)),
          height: cardHeight,
          child: Stack(
            children: [
              Container(
                height: cardHeight,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(borderRadius),
                  border: Border.all(
                    color: AppColors.divider,
                    width: 1.0,
                  ),
                ),
              ),

              SizedBox(
                height: 250,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(borderRadius),
                  child: Image.network(
                    imagesToDisplay.isNotEmpty ? imagesToDisplay[0] : 'https://via.placeholder.com/400x250?text=Hotel+Image',
                    width: double.infinity,
                    height: 250,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: Center(
                          child: Icon(
                            Icons.image_not_supported_outlined,
                            color: Colors.grey[400],
                            size: 40,
                          ),
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                        ),
                      );
                    },
                  ),
                ),
              ),

              // Action buttons - RTL responsive
              Positioned(
                top: AppDimensions.paddingSizeSmall,
                right: Directionality.of(context) == TextDirection.rtl ? null : AppDimensions.paddingSizeSmall,
                left: Directionality.of(context) == TextDirection.rtl ? AppDimensions.paddingSizeSmall : null,
                child: Column(
                  children: [
                    // Share button
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _showShareDialog,
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.95),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.divider.withValues(alpha: 0.3),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.08),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.share_outlined,
                            color: AppColors.primary,
                            size: 18,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Favorite button
                    _buildFavoriteButton(),
                  ],
                ),
              ),



              // Details container
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: detailsHeight,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Directionality.of(context) == TextDirection.rtl
                          ? Radius.circular(AppDimensions.paddingSizeOverLarge)
                          : Radius.circular(borderRadius),
                      topRight: Directionality.of(context) == TextDirection.rtl
                          ? Radius.circular(borderRadius)
                          : Radius.circular(AppDimensions.paddingSizeOverLarge),
                      bottomLeft: Radius.circular(borderRadius),
                      bottomRight: Radius.circular(borderRadius),
                    ),
                    border: Border.all(color: AppColors.divider),
                  ),
                  padding: EdgeInsets.fromLTRB(
                      AppDimensions.paddingSizeDefault,
                      AppDimensions.paddingSizeSmall,
                      AppDimensions.paddingSizeDefault,
                      AppDimensions.paddingSizeSmall // Reduced bottom padding
                      ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Hotel name
                      Text(
                        widget.hotel.name ?? 'Blue Lagoon Resort',
                        style: const TextStyle(
                          fontSize: AppDimensions.fontSizeExtraLarge,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                                            
                                            ),
                                                   Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: List.generate(
                            (widget.hotel.starRating ?? 5).toInt(), (index) {
                          return const Padding(
                            padding: EdgeInsets.symmetric(vertical: 1.0),
                            child: Icon(
                              Icons.star,
                              color: AppColors.warning,
                              size: 14,
                            ),
                          );
                        }),
                      ),
                      // Resort badge
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: AppDimensions.paddingSizeSmall,
                            vertical: AppDimensions.paddingSizeExtraSmall),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(255, 7, 105, 20),
                          borderRadius: BorderRadius.circular(
                              AppDimensions.radiusSmall + 1),
                          border: Border.all(
                              color: const Color.fromARGB(255, 60, 245, 233).withAlpha(60)),
                        ),
                        child: Text(
                          widget.hotel.accommodationType ?? 'Resort',
                          style: TextStyle(
                            fontSize: AppDimensions.fontSizeDefault,
                            color: AppColors.background,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                    

 
               
                      SizedBox(height: AppDimensions.paddingSizeExtraSmall),

                      // Location info
                      Row(
                        children: [SizedBox(child: Row(children: [const Icon(Icons.location_on,
                              color: AppColors.primary, size: 14),
                          SizedBox(width: AppDimensions.paddingSizeExtraSmall),
                          Text(
                            "${widget.hotel.locality ?? ''}, ${widget.hotel.city ?? ''}",
                            style: const TextStyle(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w500,
                              fontSize: AppDimensions.fontSizeExtraSmall,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),],),),SizedBox(width: 10,),
                          Text('-'),SizedBox(width: 10,),

                          SizedBox(child: Text(
                            "${widget.hotel.distanceFromSearchedEntity ?? ''}"              ,       style: const TextStyle(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w500,
                              fontSize: AppDimensions.fontSizeExtraSmall,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),),
                          
                        ],
                      ),
                      SizedBox(height: AppDimensions.paddingSizeExtraSmall),

                      // Horizontally scrollable features - TWO ROWS
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // First row of features
                          SizedBox(
                            height: 32, // Reduced height
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: firstRowFeatures.length,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: EdgeInsets.only(
                                      right: AppDimensions.paddingSizeSmall),
                                  child: _buildFeatureChip(
                                    firstRowFeatures[index].icon,
                                    firstRowFeatures[index].text,
                                  ),
                                );
                              },
                            ),
                          ),

                          SizedBox(height: 4), // Reduced spacing

                          // Second row of features
                          SizedBox(
                            height: 32, // Reduced height
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: secondRowFeatures.length,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: EdgeInsets.only(
                                      right: AppDimensions.paddingSizeSmall),
                                  child: _buildFeatureChip(
                                    secondRowFeatures[index].icon,
                                    secondRowFeatures[index].text,
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 8), // Reduced spacing

                      // Price and remaining info
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Price information
                          Column(
                            crossAxisAlignment: Directionality.of(context) == TextDirection.rtl
                                ? CrossAxisAlignment.end
                                : CrossAxisAlignment.start,
                            children: [
                              // Original price and offer percentage row
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  // Offer price (crossed out)
                                  if (widget.hotel.fareDetail!.offerPrice != null)
                                    Text(
                                      '₹${widget.hotel.fareDetail!.offerPrice?.toStringAsFixed(0) ?? '0'}',
                                      style: TextStyle(
                                        fontSize: AppDimensions.fontSizeSmall,
                                        color: AppColors.textLight,
                                        decoration: TextDecoration.lineThrough,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  SizedBox(width: AppDimensions.paddingSizeExtraSmall),
                                  // Offer percentage
                                  if (widget.hotel.fareDetail!.offerPercentage != null &&
                                      widget.hotel.fareDetail!.offerPercentage!.isNotEmpty)
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: AppDimensions.paddingSizeExtraSmall,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.secondary.withAlpha(20),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        widget.hotel.fareDetail!.offerPercentage!,
                                        style: TextStyle(
                                          fontSize: AppDimensions.fontSizeExtraSmall,
                                          color: AppColors.secondary,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              SizedBox(height: 4),
                              // Current price per night
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  Text(
                                    '₹${widget.hotel.fareDetail!.totalPrice?.toStringAsFixed(0) ?? '0'}',
                                    style: const TextStyle(
                                      fontSize: AppDimensions.fontSizeOverLarge,
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.w800,
                                      height: 1.0,
                                    ),
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    '/ night',
                                    style: TextStyle(
                                      fontSize: AppDimensions.fontSizeSmall,
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 2),
                              // Total for 3 nights
                              Text(
                                'Total: ₹${((widget.hotel.fareDetail!.totalPrice ?? 0) * 3).toStringAsFixed(0)} for 3 nights',
                                style: TextStyle(
                                  fontSize: AppDimensions.fontSizeExtraSmall,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),

                          // // Tax info tag
                          // Container(
                          //   padding: EdgeInsets.symmetric(
                          //       vertical: AppDimensions.paddingSizeSmall,
                          //       horizontal: AppDimensions.paddingSizeDefault),
                          //   decoration: BoxDecoration(
                          //     color: AppColors.secondary.withAlpha(20),
                          //     borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                          //     border: Border.all(color: AppColors.secondary.withAlpha(60)),
                          //   ),
                          //   child: Column(
                          //     children: [
                          //       Text(
                          //         'GST',
                          //         style: TextStyle(
                          //           color: AppColors.secondary,
                          //           fontWeight: FontWeight.w700,
                          //           fontSize: AppDimensions.fontSizeExtraSmall,
                          //         ),
                          //       ),
                          //       Text(
                          //         '18%',
                          //         style: TextStyle(
                          //           color: AppColors.secondary,
                          //           fontWeight: FontWeight.w800,
                          //           fontSize: AppDimensions.fontSizeSmall,
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
Positioned(
  bottom: 0,
  left: 0,
  right: 0,
  child: Container(
    height: 45,
    decoration: BoxDecoration(
      color: AppColors.warning.withAlpha(60),
      borderRadius: BorderRadius.only(
        bottomLeft: Radius.circular(AppDimensions.radiusExtraLarge),
        bottomRight: Radius.circular(AppDimensions.radiusExtraLarge),
      ),
    
    ),
    child: Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingSizeDefault),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                Icons.offline_bolt_rounded,
                color: AppColors.secondary,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Limited time offer!',
                style: TextStyle(
                  color: AppColors.secondary,
                  fontWeight: FontWeight.w600,
                  fontSize: AppDimensions.fontSizeLarge,
                ),
              ),
            ],
          ),
          Text(
            'Save 15% today',
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
              fontSize: AppDimensions.fontSizeSmall,
            ),
          ),
        ],
      ),
    ),
  ),
),

              // Rating widget - RTL responsive
              Positioned(
                right: Directionality.of(context) == TextDirection.rtl ? null : 0,
                left: Directionality.of(context) == TextDirection.rtl ? 0 : null,
                top: 156,
                child: Container(
                  width: 130,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topRight: Directionality.of(context) == TextDirection.rtl
                          ? Radius.zero
                          : Radius.circular(AppDimensions.paddingSizeOverLarge),
                      topLeft: Directionality.of(context) == TextDirection.rtl
                          ? Radius.circular(AppDimensions.paddingSizeOverLarge)
                          : Radius.zero,
                      bottomLeft: Directionality.of(context) == TextDirection.rtl
                          ? Radius.zero
                          : Radius.circular(AppDimensions.paddingSizeDefault),
                      bottomRight: Directionality.of(context) == TextDirection.rtl
                          ? Radius.circular(AppDimensions.paddingSizeDefault)
                          : Radius.zero,
                    ),
                    border: Border.all(
                      color: AppColors.divider,
                      width: 1.0,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.neutralDark.withAlpha(25),
                        blurRadius: 10,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),

                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Rating Score Container
                        Container(
                          height: 38,
                          width: 38,
                          decoration: BoxDecoration(
                            color: AppColors.secondary,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              '${widget.hotel.userRating ?? 0}',
                              style: const TextStyle(
                                color: AppColors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: AppDimensions.fontSizeSmall,
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Rating Details Column
                        Expanded(
                          child: Column(
                            crossAxisAlignment: Directionality.of(context) == TextDirection.rtl
                                ? CrossAxisAlignment.end
                                : CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                                   // Rating Category
                              Text(
                                widget.hotel.userRatingCategory ?? '',
                                style: const TextStyle(
                                  fontSize: AppDimensions.fontSizeSmall,
                                  color: AppColors.secondary,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                               const SizedBox(height: 2),
                              // Reviews Count Row
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    '${widget.hotel.userRatingCount ?? 0}',
                                    style: const TextStyle(
                                      color: AppColors.textLight,
                                      fontSize: AppDimensions.fontSizeExtraSmall,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    'hotel.reviews.reviews'.tr,
                                    style: const TextStyle(
                                      color: AppColors.textLight,
                                      fontSize: AppDimensions.fontSizeExtraSmall,
                                    ),
                                  ),
                                ],
                              ),

                             

                         
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureChip(IconData icon, String text) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingSizeSmall,
          vertical: 4 // Fixed smaller padding
          ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall * 2),
        border: Border.all(
            color: AppColors.divider
                .withAlpha(100)), // Added border for better definition
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon,
              size: 14,
              color: AppColors.primary.withAlpha(180)), // Smaller icon
          SizedBox(width: 4), // Smaller spacing
          Text(
            text,
            style: TextStyle(
              fontSize: AppDimensions.fontSizeSmall,
              color: AppColors.primary.withAlpha(180),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteButton() {
    final wishlistState = ref.watch(wishlistProvider);
    final wishlistNotifier = ref.read(wishlistProvider.notifier);
    final bool isInWishlist = wishlistState.wishlistItems.any(
      (hotel) => hotel.hotelId?.toString() == widget.hotel.hotelId?.toString(),
    );

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          await wishlistNotifier.toggleWishlist(widget.hotel);

          // Show a snackbar to indicate the action
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(isInWishlist
                    ? 'wishlist.removeFromWishlist'.tr
                    : 'wishlist.addToWishlist'.tr),
                duration: const Duration(seconds: 1),
                behavior: SnackBarBehavior.floating,
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: isInWishlist
                ? AppColors.error.withValues(alpha: 0.95)
                : Colors.white.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isInWishlist
                  ? AppColors.error.withValues(alpha: 0.3)
                  : AppColors.divider.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            isInWishlist ? Icons.favorite : Icons.favorite_border,
            color: isInWishlist ? Colors.white : AppColors.error,
            size: 18,
          ),
        ),
      ),
    );
  }
}

// Feature item model
class FeatureItem {
  final IconData icon;
  final String text;

  FeatureItem(this.icon, this.text);
}