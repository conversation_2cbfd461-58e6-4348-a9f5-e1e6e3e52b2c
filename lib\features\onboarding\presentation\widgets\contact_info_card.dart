import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/shared/widgets/custombutton_widget.dart';

class ContactInfoCard extends StatefulWidget {
  final String initialEmail;
  final String initialPhone;
  final String loginMethod;
  final Function(String) onEmailChanged;
  final Function(String) onPhoneChanged;
  final VoidCallback onContinue;
  final VoidCallback onBack;

  const ContactInfoCard({
    super.key,
    required this.initialEmail,
    required this.initialPhone,
    required this.loginMethod,
    required this.onEmailChanged,
    required this.onPhoneChanged,
    required this.onContinue,
    required this.onBack,
  });

  @override
  State<ContactInfoCard> createState() => _ContactInfoCardState();
}

class _ContactInfoCardState extends State<ContactInfoCard>
    with SingleTickerProviderStateMixin {
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController(text: widget.initialEmail);
    _phoneController = TextEditingController(text: widget.initialPhone);
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _handleContinue() {
    if (_formKey.currentState!.validate()) {
      widget.onEmailChanged(_emailController.text.trim());
      widget.onPhoneChanged(_phoneController.text.trim());
      widget.onContinue();
    }
  }

  String get _primaryField => widget.loginMethod == 'email' ? 'email' : 'phone';
  String get _secondaryField => widget.loginMethod == 'email' ? 'phone' : 'email';

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Back button
          Row(
            children: [
              IconButton(
                onPressed: widget.onBack,
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textLight,
                ),
              ),
              const Spacer(),
            ],
          ),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),

                  // Emoji and title with fade animation
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      children: [
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: const Center(
                            child: Text(
                              '📧',
                              style: TextStyle(fontSize: 48),
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),

                        Text(
                          'Let\'s add your contact info',
                          style: AppTextStyles.headline1.copyWith(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.text,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 12),

                        Text(
                          widget.loginMethod == 'email'
                              ? 'We\'ll need your phone number for booking confirmations'
                              : 'We\'ll need your email for booking confirmations',
                          style: AppTextStyles.bodyText1.copyWith(
                            fontSize: 16,
                            color: AppColors.textLight,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 48),

                  // Form with slide animation
                  SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // Primary field (already filled, read-only)
                            _buildInputField(
                              controller: widget.loginMethod == 'email' 
                                  ? _emailController 
                                  : _phoneController,
                              label: widget.loginMethod == 'email' 
                                  ? 'Email Address' 
                                  : 'Phone Number',
                              icon: widget.loginMethod == 'email' 
                                  ? Icons.email_outlined 
                                  : Icons.phone_outlined,
                              isReadOnly: true,
                              helperText: 'From your login',
                            ),

                            const SizedBox(height: 24),

                            // Secondary field (user needs to fill)
                            _buildInputField(
                              controller: widget.loginMethod == 'email' 
                                  ? _phoneController 
                                  : _emailController,
                              label: widget.loginMethod == 'email' 
                                  ? 'Phone Number' 
                                  : 'Email Address',
                              icon: widget.loginMethod == 'email' 
                                  ? Icons.phone_outlined 
                                  : Icons.email_outlined,
                              isReadOnly: false,
                              isRequired: widget.loginMethod == 'email', // Phone optional for email login
                              validator: widget.loginMethod == 'email' 
                                  ? _validatePhone 
                                  : _validateEmail,
                            ),

                            const SizedBox(height: 32),

                            // Info box
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.primary.withOpacity(0.1),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.security,
                                    color: AppColors.primary,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      'Your information is secure and will only be used for booking confirmations',
                                      style: AppTextStyles.bodyText2.copyWith(
                                        color: AppColors.primary,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 60),

                  // Continue button
                  SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: CustombuttonWidget(
                        text: 'Continue',
                        backgroundColor: AppColors.primary,
                        textColor: Colors.white,
                        borderRadius: 16,
                        height: 56,
                        isFullWidth: true,
                        onPressed: _handleContinue,
                        textStyle: AppTextStyles.button.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required bool isReadOnly,
    bool isRequired = true,
    String? helperText,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isReadOnly ? Colors.grey.shade50 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isReadOnly 
              ? Colors.grey.shade200 
              : AppColors.primary.withOpacity(0.2),
        ),
        boxShadow: isReadOnly ? null : [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        readOnly: isReadOnly,
        style: AppTextStyles.bodyText1.copyWith(
          fontSize: 16,
          color: isReadOnly ? AppColors.textLight : AppColors.text,
        ),
        decoration: InputDecoration(
          labelText: label,
          helperText: helperText,
          prefixIcon: Icon(
            icon,
            color: isReadOnly ? AppColors.textLight : AppColors.primary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 20,
            horizontal: 16,
          ),
        ),
        validator: isRequired ? validator : null,
      ),
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter your email address';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    // Phone is optional for email login
    if (value != null && value.trim().isNotEmpty) {
      if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
        return 'Please enter a valid phone number';
      }
    }
    return null;
  }
}
