/// Use case for checking user authentication status
library;

import '../repositories/splash_repository.dart';

/// Use case to check if user is authenticated
class CheckAuthenticationStatus {
  final SplashRepository _repository;

  CheckAuthenticationStatus(this._repository);

  /// Execute the use case
  /// Returns true if user is logged in, false otherwise
  Future<bool> call() async {
    try {
      return await _repository.isUserLoggedIn();
    } catch (e) {
      // On error, assume user is not logged in
      return false;
    }
  }
}
