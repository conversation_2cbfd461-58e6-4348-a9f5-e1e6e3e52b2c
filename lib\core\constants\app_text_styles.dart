// constants/app_text_styles.dart
import 'package:flutter/material.dart';
import 'app_colors.dart';
import '../utils/responsive_helper.dart';

class AppTextStyles {
  static const headline1 = TextStyle(
    fontSize: 28.0,
    fontWeight: FontWeight.bold,
    color: AppColors.text,
  );
  
  static const headline2 = TextStyle(
    fontSize: 24.0,
    fontWeight: FontWeight.bold,
    color: AppColors.text,
  );
  
  static const headline3 = TextStyle(
    fontSize: 20.0,
    fontWeight: FontWeight.bold,
    color: AppColors.text,
  );
  
  static const subtitle1 = TextStyle(
    fontSize: 18.0,
    fontWeight: FontWeight.w600,
    color: AppColors.text,
  );
  
  static const subtitle2 = TextStyle(
    fontSize: 16.0,
    fontWeight: FontWeight.w600,
    color: AppColors.text,
  );
  
  static const bodyText1 = TextStyle(
    fontSize: 16.0,
    color: AppColors.text,
  );
  
  static const bodyText2 = TextStyle(
    fontSize: 14.0,
    color: AppColors.textLight,
  );
  
  static const button = TextStyle(
    fontSize: 16.0,
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );
  
  static const caption = TextStyle(
    fontSize: 12.0,
    color: AppColors.textLight,
  );

  // RESPONSIVE TEXT STYLES

  /// Responsive headline styles
  static TextStyle headline1R(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 28.0),
    fontWeight: FontWeight.bold,
    color: AppColors.text,
  );

  static TextStyle headline2R(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 24.0),
    fontWeight: FontWeight.bold,
    color: AppColors.text,
  );

  static TextStyle headline3R(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 20.0),
    fontWeight: FontWeight.bold,
    color: AppColors.text,
  );

  /// Responsive subtitle styles
  static TextStyle subtitle1R(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 18.0),
    fontWeight: FontWeight.w600,
    color: AppColors.text,
  );

  static TextStyle subtitle2R(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16.0),
    fontWeight: FontWeight.w600,
    color: AppColors.text,
  );

  /// Responsive body text styles
  static TextStyle bodyText1R(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16.0),
    color: AppColors.text,
  );

  static TextStyle bodyText2R(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14.0),
    color: AppColors.textLight,
  );

  /// Responsive button style
  static TextStyle buttonR(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16.0),
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );

  /// Responsive caption style
  static TextStyle captionR(BuildContext context) => TextStyle(
    fontSize: ResponsiveHelper.getResponsiveFontSize(context, 12.0),
    color: AppColors.textLight,
  );
}