﻿import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/core/network/network_info.dart';
import '../../domain/entities/hotel_entity.dart';
import '../../domain/entities/room_entity.dart';
import 'package:kind_ali/features/search/domain/entities/search_entity.dart';
import '../../domain/repositories/hotel_repository.dart';
import 'package:kind_ali/features/hotel/data/datasources/hotel_local_datasource.dart';
import 'package:kind_ali/features/hotel/data/datasources/hotel_remote_datasource.dart';
import '../../../hotel/data/models/hotel_details.dart';

/// Implementation of hotel repository
class HotelRepositoryImpl implements HotelRepository {
  final HotelRemoteDataSource remoteDataSource;
  final HotelLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  HotelRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<HotelEntity>>> getHotels() async {
    try {
      if (await networkInfo.isConnected) {
        // Try to get from remote
        try {
          final remoteHotels = await remoteDataSource.getHotels();
          await localDataSource.cacheHotels(remoteHotels);
          return Right(_mapHotelsToEntities(remoteHotels));
        } catch (e) {
          // If remote fails, try local cache
          return await _getHotelsFromLocal();
        }
      } else {
        // No internet, get from local
        return await _getHotelsFromLocal();
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, HotelEntity>> getHotelDetails(int hotelId) async {
    try {
      if (await networkInfo.isConnected) {
        try {
          final remoteHotel = await remoteDataSource.getHotelDetails(hotelId);
          await localDataSource.cacheHotelDetails(remoteHotel);
          return Right(_mapHotelToEntity(remoteHotel));
        } catch (e) {
          // If remote fails, try local cache
          final cachedHotel = await localDataSource.getHotelDetailsFromCache(hotelId);
          if (cachedHotel != null) {
            return Right(_mapHotelToEntity(cachedHotel));
          }
          return Left(_mapExceptionToFailure(e));
        }
      } else {
        // No internet, get from local
        final cachedHotel = await localDataSource.getHotelDetailsFromCache(hotelId);
        if (cachedHotel != null) {
          return Right(_mapHotelToEntity(cachedHotel));
        }
        return const Left(NetworkFailure('No internet connection and no cached data'));
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<HotelEntity>>> searchHotels(
    HotelSearchEntity searchCriteria,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteHotels = await remoteDataSource.searchHotels(
          destination: searchCriteria.destination ?? '',
          checkIn: searchCriteria.checkInDate ?? DateTime.now(),
          checkOut: searchCriteria.checkOutDate ?? DateTime.now().add(const Duration(days: 1)),
          guests: searchCriteria.numberOfGuests ?? 2,
          rooms: searchCriteria.numberOfRooms ?? 1,
        );
        return Right(_mapHotelsToEntities(remoteHotels));
      } else {
        // No internet, filter local data
        final localHotels = await localDataSource.getHotelsFromCache();
        final filteredHotels = _filterHotelsLocally(localHotels, searchCriteria);
        return Right(_mapHotelsToEntities(filteredHotels));
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<HotelEntity>>> getNearbyHotels({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteHotels = await remoteDataSource.getNearbyHotels(
          latitude: latitude,
          longitude: longitude,
          radiusKm: radiusKm,
        );
        return Right(_mapHotelsToEntities(remoteHotels));
      } else {
        return const Left(NetworkFailure('No internet connection'));
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<RoomEntity>>> getHotelRooms(int hotelId) async {
    try {
      final hotelResult = await getHotelDetails(hotelId);
      return hotelResult.fold(
        (failure) => Left(failure),
        (hotel) {
          // Extract rooms from hotel details
          final rooms = <RoomEntity>[];
          // This would need to be implemented based on the actual room data structure
          return Right(rooms);
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<HotelEntity>>> getFilteredHotels({
    double? minPrice,
    double? maxPrice,
    double? minRating,
    List<String>? amenities,
    String? sortBy,
  }) async {
    try {
      final hotelsResult = await getHotels();
      return hotelsResult.fold(
        (failure) => Left(failure),
        (hotels) {
          var filteredHotels = hotels;
          
          // Apply filters
          if (minPrice != null) {
            filteredHotels = filteredHotels.where((h) => 
              (h.fareDetail?.totalPrice ?? 0) >= minPrice).toList();
          }
          
          if (maxPrice != null) {
            filteredHotels = filteredHotels.where((h) => 
              (h.fareDetail?.totalPrice ?? double.infinity) <= maxPrice).toList();
          }
          
          if (minRating != null) {
            filteredHotels = filteredHotels.where((h) => 
              (h.starRating ?? 0) >= minRating).toList();
          }
          
          // Apply sorting
          if (sortBy != null) {
            switch (sortBy) {
              case 'price_asc':
                filteredHotels.sort((a, b) => 
                  (a.fareDetail?.totalPrice ?? 0).compareTo(b.fareDetail?.totalPrice ?? 0));
                break;
              case 'price_desc':
                filteredHotels.sort((a, b) => 
                  (b.fareDetail?.totalPrice ?? 0).compareTo(a.fareDetail?.totalPrice ?? 0));
                break;
              case 'rating':
                filteredHotels.sort((a, b) => 
                  (b.starRating ?? 0).compareTo(a.starRating ?? 0));
                break;
            }
          }
          
          return Right(filteredHotels);
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, List<HotelEntity>>> getPopularHotels({int limit = 10}) async {
    try {
      final hotelsResult = await getHotels();
      return hotelsResult.fold(
        (failure) => Left(failure),
        (hotels) {
          // Sort by rating and take top hotels
          final popularHotels = hotels
            ..sort((a, b) => (b.starRating ?? 0).compareTo(a.starRating ?? 0));
          return Right(popularHotels.take(limit).toList());
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> addToWishlist(int hotelId) async {
    // This would be implemented with user-specific wishlist storage
    return const Right(null);
  }

  @override
  Future<Either<Failure, void>> removeFromWishlist(int hotelId) async {
    // This would be implemented with user-specific wishlist storage
    return const Right(null);
  }

  @override
  Future<Either<Failure, List<HotelEntity>>> getWishlistHotels() async {
    // This would be implemented with user-specific wishlist storage
    return const Right([]);
  }

  // Helper methods
  Future<Either<Failure, List<HotelEntity>>> _getHotelsFromLocal() async {
    try {
      if (await localDataSource.isHotelsCacheValid()) {
        final localHotels = await localDataSource.getHotelsFromCache();
        return Right(_mapHotelsToEntities(localHotels));
      } else {
        // Cache invalid, try to get from assets
        final assetHotels = await localDataSource.getHotelsFromAssets();
        return Right(_mapHotelsToEntities(assetHotels));
      }
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  List<InventoryInfoList> _filterHotelsLocally(
    List<InventoryInfoList> hotels,
    HotelSearchEntity searchCriteria,
  ) {
    var filteredHotels = hotels;
    
    // Filter by destination/city
    if (searchCriteria.destination != null && searchCriteria.destination!.isNotEmpty) {
      filteredHotels = filteredHotels.where((hotel) =>
        hotel.city?.toLowerCase().contains(searchCriteria.destination!.toLowerCase()) == true ||
        hotel.locality?.toLowerCase().contains(searchCriteria.destination!.toLowerCase()) == true ||
        hotel.name?.toLowerCase().contains(searchCriteria.destination!.toLowerCase()) == true
      ).toList();
    }
    
    return filteredHotels;
  }

  List<HotelEntity> _mapHotelsToEntities(List<InventoryInfoList> hotels) {
    return hotels.map((hotel) => _mapHotelToEntity(hotel)).toList();
  }

  HotelEntity _mapHotelToEntity(InventoryInfoList hotel) {
    return HotelEntity(
      hotelId: hotel.hotelId,
      locationId: hotel.locationId,
      name: hotel.name,
      locality: hotel.locality,
      city: hotel.city,
      userRating: hotel.userRating,
      userRatingCategory: hotel.userRatingCategory,
      starRating: hotel.starRating,
      userRatingCount: hotel.userRatingCount,
      imageUrls: hotel.imageInfoList?.map((img) => img.url ?? '').toList(),
      comfortRating: hotel.comfortRating,
      taxesAndCharges: hotel.taxesAndCharges,
      accommodationType: hotel.accommodationType,
      topOfferings: hotel.topOfferings,
      roomsCountLeft: hotel.roomsCountLeft,
      distanceFromSearchedEntity: hotel.distanceFromSearchedEntity,
      fomoTags: hotel.fomoTags?.map((tag) => tag.data ?? '').toList(),
      amenities: hotel.amenities?.map((amenity) => amenity.name ?? '').toList(),
      geoLocation: hotel.geoLocationInfo != null
          ? GeoLocationEntity(
              latitude: hotel.geoLocationInfo!.lat ?? 0.0,
              longitude: hotel.geoLocationInfo!.lon ?? 0.0,
            )
          : null,
      fareDetail: hotel.fareDetail != null
          ? FareDetailEntity(
              basePrice: hotel.fareDetail!.displayedBaseFare,
              totalPrice: hotel.fareDetail!.totalPrice,
              taxes: 0.0, // Not available in model
              discount: hotel.fareDetail!.offerPrice,
              currency: 'USD', // Default currency as not available in model
            )
          : null,
    );
  }

  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is ServerException) {
      return ServerFailure(exception.message);
    } else if (exception is CacheException) {
      return CacheFailure(exception.message);
    } else if (exception is NetworkException) {
      return NetworkFailure(exception.message);
    } else {
      return GeneralFailure('An unexpected error occurred: $exception');
    }
  }
}
