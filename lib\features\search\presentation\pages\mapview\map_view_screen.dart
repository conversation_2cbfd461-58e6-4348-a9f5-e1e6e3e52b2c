import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_details.dart';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_detail/hotel_detail_screen.dart';

class MapViewScreen extends StatefulWidget {
  final List<InventoryInfoList> hotels;
  final String destination;

  const MapViewScreen({
    super.key,
    required this.hotels,
    required this.destination,
  });

  @override
  State<MapViewScreen> createState() => _MapViewScreenState();
}

class _MapViewScreenState extends State<MapViewScreen> {
  final MapController _mapController = MapController();
  List<Marker> _markers = [];
  InventoryInfoList? _selectedHotel;
  bool _isMapReady = false;

  @override
  void initState() {
    super.initState();
    _createMarkers();
  }

  void _createMarkers() {
    List<Marker> markers = [];

    for (var hotel in widget.hotels) {
      if (hotel.geoLocationInfo != null) {
        final coordinates = hotel.geoLocationInfo;

        // Only add markers for hotels with valid coordinates
        if (coordinates!.lat != null && coordinates.lon != null) {
          final marker = Marker(
            width: 40.0,
            height: 40.0,
            point: LatLng(coordinates.lat!, coordinates.lon!),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedHotel = hotel;
                });

                // Center the map on the selected hotel
                _mapController.move(
                  LatLng(coordinates.lat!, coordinates.lon!),
                  _mapController.camera.zoom,
                );
              },
              child: Container(
                decoration: BoxDecoration(
                  color: _selectedHotel?.hotelId == hotel.hotelId
                      ? AppColors.secondary
                      : AppColors.primary,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color.fromRGBO(0, 0, 0, 0.2),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(
                    Icons.location_on,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
          );

          markers.add(marker);
        }
      }
    }

    setState(() {
      _markers = markers;
      _isMapReady = true;
    });
  }

  LatLngBounds _getBounds() {
    if (_markers.isEmpty) {
      // Default to a location if no markers
      return LatLngBounds(
        LatLng(20.5937, 78.9629), // Default to India
        LatLng(20.5937, 78.9629),
      );
    }

    double? minLat, maxLat, minLng, maxLng;

    for (var marker in _markers) {
      if (minLat == null || marker.point.latitude < minLat) {
        minLat = marker.point.latitude;
      }
      if (maxLat == null || marker.point.latitude > maxLat) {
        maxLat = marker.point.latitude;
      }
      if (minLng == null || marker.point.longitude < minLng) {
        minLng = marker.point.longitude;
      }
      if (maxLng == null || marker.point.longitude > maxLng) {
        maxLng = marker.point.longitude;
      }
    }

    return LatLngBounds(
      LatLng(minLat! - 0.05, minLng! - 0.05), // Add some padding
      LatLng(maxLat! + 0.05, maxLng! + 0.05),
    );
  }

  void _fitBounds() {
    if (_markers.isEmpty || !_isMapReady) return;

    final bounds = _getBounds();
    _mapController.fitCamera(
      CameraFit.bounds(
        bounds: bounds,
        padding: const EdgeInsets.all(50.0),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Hotels in ${widget.destination}'),
        backgroundColor: AppColors.primary,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // Map
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: _markers.isNotEmpty
                  ? _markers.first.point
                  : const LatLng(20.5937, 78.9629), // Default to India
              initialZoom: 12,
              onMapReady: () {
                Future.delayed(const Duration(milliseconds: 300), _fitBounds);
              },
            ),
            children: [
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.example.kind_ali',
              ),
              MarkerLayer(markers: _markers),
            ],
          ),

          // Selected hotel card at bottom
          if (_selectedHotel != null)
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: _buildHotelCard(_selectedHotel!),
            ),

          // Zoom controls
          Positioned(
            right: 16,
            bottom: _selectedHotel != null ? 200 : 16,
            child: Column(
              children: [
                _buildZoomButton(Icons.add, () {
                  _mapController.move(
                    _mapController.camera.center,
                    _mapController.camera.zoom + 1,
                  );
                }),
                const SizedBox(height: 8),
                _buildZoomButton(Icons.remove, () {
                  _mapController.move(
                    _mapController.camera.center,
                    _mapController.camera.zoom - 1,
                  );
                }),
              ],
            ),
          ),

          // Fit bounds button
          Positioned(
            right: 16,
            top: 16,
            child: _buildZoomButton(Icons.fit_screen, _fitBounds),
          ),
        ],
      ),
    );
  }

  Widget _buildZoomButton(IconData icon, VoidCallback onPressed) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(0, 0, 0, 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        padding: EdgeInsets.zero,
        icon: Icon(icon, color: AppColors.primary),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildHotelCard(InventoryInfoList hotel) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HotelDetailScreen(hotel: hotel),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(0, 0, 0, 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Hotel image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: hotel.imageInfoList != null && hotel.imageInfoList!.isNotEmpty
                  ? Image.network(
                      hotel.imageInfoList!.first.url!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 80,
                          height: 80,
                          color: Colors.grey[300],
                          child: const Icon(Icons.image_not_supported),
                        );
                      },
                    )
                  : Container(
                      width: 80,
                      height: 80,
                      color: Colors.grey[300],
                      child: const Icon(Icons.image_not_supported),
                    ),
            ),
            const SizedBox(width: 12),

            // Hotel details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    hotel.name ?? 'Unknown Hotel',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.location_on, size: 14, color: AppColors.primary),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          hotel.locality ?? 'Address not available',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      if (hotel.starRating != null) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.star, size: 12, color: Colors.white),
                              const SizedBox(width: 2),
                              Text(
                                '${hotel.starRating}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        '₹${hotel.fareDetail!.totalPrice ?? 0}/night',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.secondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // View details button
            Container(
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.all(8),
              child: const Icon(
                Icons.arrow_forward,
                color: AppColors.primary,
                size: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
