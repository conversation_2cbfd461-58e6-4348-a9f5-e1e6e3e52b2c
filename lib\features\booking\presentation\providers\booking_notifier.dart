/// Booking state management using Riverpod StateNotifier
library;

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../hotel/data/models/hotel_details.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_offers.dart';

/// Booking state class
class BookingState {
  final Hoteloffers? hotelOffers;
  final List<Offer> offers;
  final bool isBookingForOthers;
  final List<TextEditingController> guestFirstNameControllers;
  final List<TextEditingController> guestLastNameControllers;
  final List<String> guestTitles;
  final GlobalKey<FormState> formKey;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  final TextEditingController emailController;
  final TextEditingController phoneController;
  final TextEditingController specialRequestController;
  final String selectedTitle;
  final String selectedCountryCode;
  final String? selectedNationality;
  final double roomCharges;
  final double taxesAndFees;
  final String? appliedCouponCode;
  final double couponDiscount;
  final String? specialRequest;
  final InventoryInfoList? hotel;
  final String roomType;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int adultCount;
  final int childCount;
  final bool isLoading;
  final String? error;
  final List<String> nationalities;
  final int numberOfNights;

  const BookingState({
    this.hotelOffers,
    this.offers = const [],
    this.isBookingForOthers = false,
    this.guestFirstNameControllers = const [],
    this.guestLastNameControllers = const [],
    this.guestTitles = const [],
    required this.formKey,
    required this.firstNameController,
    required this.lastNameController,
    required this.emailController,
    required this.phoneController,
    required this.specialRequestController,
    this.selectedTitle = 'Mr.',
    this.selectedCountryCode = '+1',
    this.selectedNationality,
    this.roomCharges = 0.0,
    this.taxesAndFees = 0.0,
    this.appliedCouponCode,
    this.couponDiscount = 0.0,
    this.specialRequest,
    this.hotel,
    this.roomType = 'Deluxe Room',
    this.checkInDate,
    this.checkOutDate,
    this.adultCount = 2,
    this.childCount = 0,
    this.isLoading = false,
    this.error,
    this.nationalities = const [],
    this.numberOfNights = 1,
  });

  /// Calculate total amount
  double get totalAmount => roomCharges + taxesAndFees - couponDiscount;

  BookingState copyWith({
    Hoteloffers? hotelOffers,
    List<Offer>? offers,
    bool? isBookingForOthers,
    List<TextEditingController>? guestFirstNameControllers,
    List<TextEditingController>? guestLastNameControllers,
    List<String>? guestTitles,
    GlobalKey<FormState>? formKey,
    TextEditingController? firstNameController,
    TextEditingController? lastNameController,
    TextEditingController? emailController,
    TextEditingController? phoneController,
    TextEditingController? specialRequestController,
    String? selectedTitle,
    String? selectedCountryCode,
    String? selectedNationality,
    double? roomCharges,
    double? taxesAndFees,
    String? appliedCouponCode,
    double? couponDiscount,
    String? specialRequest,
    InventoryInfoList? hotel,
    String? roomType,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? adultCount,
    int? childCount,
    bool? isLoading,
    String? error,
    List<String>? nationalities,
    int? numberOfNights,
  }) {
    return BookingState(
      hotelOffers: hotelOffers ?? this.hotelOffers,
      offers: offers ?? this.offers,
      isBookingForOthers: isBookingForOthers ?? this.isBookingForOthers,
      guestFirstNameControllers: guestFirstNameControllers ?? this.guestFirstNameControllers,
      guestLastNameControllers: guestLastNameControllers ?? this.guestLastNameControllers,
      guestTitles: guestTitles ?? this.guestTitles,
      formKey: formKey ?? this.formKey,
      firstNameController: firstNameController ?? this.firstNameController,
      lastNameController: lastNameController ?? this.lastNameController,
      emailController: emailController ?? this.emailController,
      phoneController: phoneController ?? this.phoneController,
      specialRequestController: specialRequestController ?? this.specialRequestController,
      selectedTitle: selectedTitle ?? this.selectedTitle,
      selectedCountryCode: selectedCountryCode ?? this.selectedCountryCode,
      selectedNationality: selectedNationality ?? this.selectedNationality,
      roomCharges: roomCharges ?? this.roomCharges,
      taxesAndFees: taxesAndFees ?? this.taxesAndFees,
      appliedCouponCode: appliedCouponCode ?? this.appliedCouponCode,
      couponDiscount: couponDiscount ?? this.couponDiscount,
      specialRequest: specialRequest ?? this.specialRequest,
      hotel: hotel ?? this.hotel,
      roomType: roomType ?? this.roomType,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      adultCount: adultCount ?? this.adultCount,
      childCount: childCount ?? this.childCount,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      nationalities: nationalities ?? this.nationalities,
      numberOfNights: numberOfNights ?? this.numberOfNights,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookingState &&
        other.hotelOffers == hotelOffers &&
        other.offers == offers &&
        other.isBookingForOthers == isBookingForOthers &&
        other.selectedTitle == selectedTitle &&
        other.selectedCountryCode == selectedCountryCode &&
        other.selectedNationality == selectedNationality &&
        other.roomCharges == roomCharges &&
        other.taxesAndFees == taxesAndFees &&
        other.appliedCouponCode == appliedCouponCode &&
        other.couponDiscount == couponDiscount &&
        other.hotel == hotel &&
        other.roomType == roomType &&
        other.checkInDate == checkInDate &&
        other.checkOutDate == checkOutDate &&
        other.adultCount == adultCount &&
        other.childCount == childCount &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode => Object.hash(
        hotelOffers,
        offers,
        isBookingForOthers,
        selectedTitle,
        selectedCountryCode,
        selectedNationality,
        roomCharges,
        taxesAndFees,
        appliedCouponCode,
        couponDiscount,
        hotel,
        roomType,
        checkInDate,
        checkOutDate,
        adultCount,
        childCount,
        isLoading,
        error,
      );
}

/// Booking StateNotifier
class BookingNotifier extends StateNotifier<BookingState> {
  BookingNotifier()
      : super(BookingState(
          formKey: GlobalKey<FormState>(),
          firstNameController: TextEditingController(),
          lastNameController: TextEditingController(),
          emailController: TextEditingController(),
          phoneController: TextEditingController(),
          specialRequestController: TextEditingController(),
        )) {
    loadHotelOffers();
    _loadNationalities();
  }

  @override
  void dispose() {
    state.firstNameController.dispose();
    state.lastNameController.dispose();
    state.emailController.dispose();
    state.phoneController.dispose();
    state.specialRequestController.dispose();
    
    // Dispose guest controllers
    for (final controller in state.guestFirstNameControllers) {
      controller.dispose();
    }
    for (final controller in state.guestLastNameControllers) {
      controller.dispose();
    }
    
    super.dispose();
  }

  /// Load hotel offers from JSON
  Future<void> loadHotelOffers() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final String jsonString = await rootBundle.loadString('assets/json/hoteloffers.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final hotelOffers = Hoteloffers.fromJson(jsonData);
      final offers = hotelOffers.data?.offers ?? [];

      state = state.copyWith(
        hotelOffers: hotelOffers,
        offers: offers,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load hotel offers: $e',
      );
    }
  }

  /// Load nationalities list
  void _loadNationalities() {
    const nationalities = [
      'United States',
      'Canada',
      'United Kingdom',
      'Germany',
      'France',
      'Italy',
      'Spain',
      'Australia',
      'Japan',
      'China',
      'India',
      'Brazil',
      'Mexico',
      'Russia',
      'South Korea',
      'Netherlands',
      'Sweden',
      'Norway',
      'Denmark',
      'Switzerland',
    ];

    state = state.copyWith(nationalities: nationalities);
  }

  /// Initialize booking with hotel data
  void initBooking({
    InventoryInfoList? hotel,
    String? roomType,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int adultCount = 2,
    int childCount = 0,
  }) {
    // Calculate number of nights
    int nights = 1;
    if (checkInDate != null && checkOutDate != null) {
      nights = checkOutDate.difference(checkInDate).inDays;
      if (nights <= 0) nights = 1;
    }

    state = state.copyWith(
      hotel: hotel,
      roomType: roomType ?? 'Deluxe Room',
      checkInDate: checkInDate,
      checkOutDate: checkOutDate,
      adultCount: adultCount,
      childCount: childCount,
      numberOfNights: nights,
    );
  }

  /// Set selected title
  void setSelectedTitle(String title) {
    state = state.copyWith(selectedTitle: title);
  }

  /// Set selected country code
  void setSelectedCountryCode(String code) {
    state = state.copyWith(selectedCountryCode: code);
  }

  /// Set selected nationality
  void setSelectedNationality(String? nationality) {
    state = state.copyWith(selectedNationality: nationality);
  }

  /// Apply coupon code
  void applyCoupon(String code, double discount) {
    state = state.copyWith(
      appliedCouponCode: code,
      couponDiscount: discount,
    );
  }

  /// Remove coupon code
  void removeCoupon() {
    state = state.copyWith(
      appliedCouponCode: null,
      couponDiscount: 0.0,
    );
  }

  /// Set special request
  void setSpecialRequest(String? request) {
    state = state.copyWith(specialRequest: request);
  }

  /// Set room charges
  void setRoomCharges(double charges) {
    state = state.copyWith(roomCharges: charges);
  }

  /// Set taxes and fees
  void setTaxesAndFees(double fees) {
    state = state.copyWith(taxesAndFees: fees);
  }

  /// Validate form
  bool validateForm() {
    return state.formKey.currentState?.validate() ?? false;
  }

  /// Set booking for others
  void setIsBookingForOthers(bool value) {
    state = state.copyWith(isBookingForOthers: value);
  }

  /// Get guest title by index
  String getGuestTitle(int index) {
    if (index < state.guestTitles.length) {
      return state.guestTitles[index];
    }
    return 'Mr.'; // Default title
  }

  /// Set guest title by index
  void setGuestTitle(int index, String title) {
    final updatedTitles = List<String>.from(state.guestTitles);

    // Ensure the list is long enough
    while (updatedTitles.length <= index) {
      updatedTitles.add('Mr.');
    }

    updatedTitles[index] = title;
    state = state.copyWith(guestTitles: updatedTitles);
  }

  /// Get guest first name controller by index
  TextEditingController getGuestFirstNameController(int index) {
    if (index < state.guestFirstNameControllers.length) {
      return state.guestFirstNameControllers[index];
    }

    // Create new controllers if needed
    final updatedControllers = List<TextEditingController>.from(state.guestFirstNameControllers);
    while (updatedControllers.length <= index) {
      updatedControllers.add(TextEditingController());
    }

    state = state.copyWith(guestFirstNameControllers: updatedControllers);
    return updatedControllers[index];
  }

  /// Get guest last name controller by index
  TextEditingController getGuestLastNameController(int index) {
    if (index < state.guestLastNameControllers.length) {
      return state.guestLastNameControllers[index];
    }

    // Create new controllers if needed
    final updatedControllers = List<TextEditingController>.from(state.guestLastNameControllers);
    while (updatedControllers.length <= index) {
      updatedControllers.add(TextEditingController());
    }

    state = state.copyWith(guestLastNameControllers: updatedControllers);
    return updatedControllers[index];
  }

  /// Get unique offers (removing duplicates based on coupon code)
  List<Offer> getUniqueOffers() {
    final Map<String, Offer> uniqueOffers = {};
    for (final offer in state.offers) {
      if (offer.couponCode != null && !uniqueOffers.containsKey(offer.couponCode)) {
        uniqueOffers[offer.couponCode!] = offer;
      }
    }
    return uniqueOffers.values.toList();
  }

  /// Find offer by coupon code
  Offer? findOfferByCode(String couponCode) {
    try {
      return state.offers.firstWhere((offer) => offer.couponCode == couponCode);
    } catch (e) {
      return null;
    }
  }

  /// Check if coupon is valid
  bool isCouponValid(String couponCode) {
    return findOfferByCode(couponCode) != null;
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh offers
  Future<void> refreshOffers() async {
    await loadHotelOffers();
  }
}

/// Booking provider
final bookingProvider = StateNotifierProvider<BookingNotifier, BookingState>(
  (ref) => BookingNotifier(),
);
