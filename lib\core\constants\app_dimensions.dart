import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';

class AppDimensions {
  // Legacy constants for backward compatibility
  static const double fontSizeExtraSmall = 10.0;
  static const double fontSizeSmall = 12.0;
  static const double fontSizeDefault = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeExtraLarge = 18.0;
  static const double fontSizeOverLarge = 24.0;
  static const double fontsizeOnTop = 35.0;

  static const double paddingSizeExtraSmall = 5.0;
  static const double paddingSizeSmall = 10.0;
  static const double paddingSizeDefault = 15.0;
  static const double paddingSizeLarge = 20.0;
  static const double paddingSizeExtraLarge = 25.0;
  static const double paddingSizeOverLarge = 30.0;
  static const double paddingSizeOverExtraLarge = 35.0;

  static const double radiusSmall = 5.0;
  static const double radiusDefault = 10.0;
  static const double radiusLarge = 15.0;
  static const double radiusExtraLarge = 20.0;

  static const int messageInputLength = 250;
  static const double webMaxWidth = 1170;

  // NEW RESPONSIVE METHODS

  /// Responsive font sizes
  static double fontSizeExtraSmallR(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, 10.0);
  static double fontSizeSmallR(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, 12.0);
  static double fontSizeDefaultR(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, 14.0);
  static double fontSizeLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, 16.0);
  static double fontSizeExtraLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, 18.0);
  static double fontSizeOverLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, 24.0);
  static double fontsizeOnTopR(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, 35.0);

  /// Responsive padding sizes
  static double paddingSizeExtraSmallR(BuildContext context) =>
      ResponsiveHelper.getResponsivePadding(context, 5.0);
  static double paddingSizeSmallR(BuildContext context) =>
      ResponsiveHelper.getResponsivePadding(context, 10.0);
  static double paddingSizeDefaultR(BuildContext context) =>
      ResponsiveHelper.getResponsivePadding(context, 15.0);
  static double paddingSizeLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsivePadding(context, 20.0);
  static double paddingSizeExtraLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsivePadding(context, 25.0);
  static double paddingSizeOverLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsivePadding(context, 30.0);
  static double paddingSizeOverExtraLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsivePadding(context, 35.0);

  /// Responsive border radius
  static double radiusSmallR(BuildContext context) =>
      ResponsiveHelper.getResponsiveBorderRadius(context, 5.0);
  static double radiusDefaultR(BuildContext context) =>
      ResponsiveHelper.getResponsiveBorderRadius(context, 10.0);
  static double radiusLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsiveBorderRadius(context, 15.0);
  static double radiusExtraLargeR(BuildContext context) =>
      ResponsiveHelper.getResponsiveBorderRadius(context, 20.0);

  /// Responsive button heights
  static double buttonHeightSmall(BuildContext context) =>
      ResponsiveHelper.getResponsiveButtonHeight(context, ButtonSize.small);
  static double buttonHeightMedium(BuildContext context) =>
      ResponsiveHelper.getResponsiveButtonHeight(context, ButtonSize.medium);
  static double buttonHeightLarge(BuildContext context) =>
      ResponsiveHelper.getResponsiveButtonHeight(context, ButtonSize.large);

  /// Responsive spacing shortcuts
  static double spacingXS(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, ResponsiveSpacing.xs);
  static double spacingSM(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, ResponsiveSpacing.sm);
  static double spacingMD(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, ResponsiveSpacing.md);
  static double spacingLG(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, ResponsiveSpacing.lg);
  static double spacingXL(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, ResponsiveSpacing.xl);
  static double spacingXXL(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, ResponsiveSpacing.xxl);
}
