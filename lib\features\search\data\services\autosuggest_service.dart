/// Autosuggest API service for search functionality
library;

import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../../../core/constants/api_constants.dart';
import '../models/autosuggest_models.dart';

/// Service class for autosuggest API calls
class AutosuggestService {
  final http.Client _client;

  AutosuggestService(this._client);

  /// Get autosuggest results from API
  ///
  /// [query] - The search query string
  /// Returns [AutosuggestResponse] with suggestions or throws exception
  Future<AutosuggestResponse> getAutosuggest(String query) async {
    debugPrint('🔍 [AUTOSUGGEST] Starting API call for query: "$query"');

    try {
      // Validate input
      if (query.trim().isEmpty) {
        debugPrint('⚠️ [AUTOSUGGEST] Empty query provided, returning empty results');
        return AutosuggestResponse(
          suggestions: [],
          success: true,
          message: 'Empty query',
        );
      }

      // Build URI with query parameters - using API constants
      final queryParams = {
        'term': query.trim(),  // Exact same parameter as Insomnia: ?term=value
        // Removed limit parameter since it's not in your Insomnia request
      };

      final uri = Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.autoSuggest}').replace(
        queryParameters: queryParams,
      );

      print('🌐 [AUTOSUGGEST] Making HTTP GET request to: $uri');
      print('🔗 [AUTOSUGGEST] Full URL: ${uri.toString()}');
      print('🏗️ [AUTOSUGGEST] Using API Constants - Base: ${ApiConstants.fullBaseUrl}, Endpoint: ${ApiConstants.autoSuggest}');
      print('📋 [AUTOSUGGEST] Query Parameters: $queryParams');
      print('📤 [AUTOSUGGEST] Request headers: Content-Type: application/json, Accept: application/json');

      // Make HTTP request using API constants
      final response = await _client.get(
        uri,
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
          'Accept': ApiConstants.applicationJson,
        },
      ).timeout(Duration(seconds: ApiConstants.connectionTimeout));

      print('📥 [AUTOSUGGEST] Response received - Status Code: ${response.statusCode}');
      print('📄 [AUTOSUGGEST] Response Body Length: ${response.body.length} characters');
      print('📋 [AUTOSUGGEST] Raw Response Body: ${response.body}');

      // Handle response
      if (response.statusCode == 200) {
        print('✅ [AUTOSUGGEST] Success! Parsing JSON response...');
        final Map<String, dynamic> jsonData = json.decode(response.body);
        final result = AutosuggestResponse.fromJson(jsonData);
        print('📊 [AUTOSUGGEST] Parsed ${result.suggestions.length} suggestions successfully');

        // Log first few suggestions for debugging
        if (result.suggestions.isNotEmpty) {
          print('🎯 [AUTOSUGGEST] Sample suggestions:');
          for (int i = 0; i < result.suggestions.length && i < 3; i++) {
            final suggestion = result.suggestions[i];
            print('   ${i + 1}. ${suggestion.name} (${suggestion.type}) - ${suggestion.country ?? 'Unknown'}');
          }
        }

        return result;
      } else if (response.statusCode == 404) {
        print('🔍 [AUTOSUGGEST] No suggestions found (404) - returning empty results');
        // Handle not found - return empty results
        return AutosuggestResponse(
          suggestions: [],
          success: false,
          message: 'No suggestions found',
        );
      } else {
        print('❌ [AUTOSUGGEST] API Error - Status Code: ${response.statusCode}');
        print('📄 [AUTOSUGGEST] Error Response Body: ${response.body}');
        throw AutosuggestException(
          'Failed to fetch autosuggest: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } on TimeoutException {
      print('⏰ [AUTOSUGGEST] Request timeout after 30 seconds');
      throw AutosuggestException(
        'Request timeout - please check your connection',
        isTimeout: true,
      );
    } on FormatException catch (e) {
      print('🔧 [AUTOSUGGEST] JSON parsing error: $e');
      throw AutosuggestException(
        'Invalid response format: $e',
        isFormatError: true,
      );
    } catch (e) {
      if (e is AutosuggestException) {
        print('🚨 [AUTOSUGGEST] Rethrowing AutosuggestException: ${e.message}');
        rethrow;
      }
      print('💥 [AUTOSUGGEST] Unexpected error: $e');
      print('🔍 [AUTOSUGGEST] Error type: ${e.runtimeType}');
      throw AutosuggestException(
        'Network error occurred: $e',
        isNetworkError: true,
      );
    }
  }

  /// Test method to manually check API response format
  /// Call this method to see what your API actually returns
  Future<void> testApiResponse(String testQuery) async {
    print('🧪 [AUTOSUGGEST_TEST] Testing API with query: "$testQuery"');

    try {
      // Using exact same format as Insomnia: ?term=value with API constants
      final uri = Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.autoSuggest}').replace(
        queryParameters: {'term': testQuery},
      );

      print('🧪 [AUTOSUGGEST_TEST] Test URL: $uri');

      final response = await _client.get(uri);

      print('🧪 [AUTOSUGGEST_TEST] Status: ${response.statusCode}');
      print('🧪 [AUTOSUGGEST_TEST] Headers: ${response.headers}');
      print('🧪 [AUTOSUGGEST_TEST] Raw Body: ${response.body}');

      if (response.statusCode == 200) {
        try {
          final jsonData = json.decode(response.body);
          print('🧪 [AUTOSUGGEST_TEST] Parsed JSON: $jsonData');
          print('🧪 [AUTOSUGGEST_TEST] JSON Type: ${jsonData.runtimeType}');

          if (jsonData is Map) {
            print('🧪 [AUTOSUGGEST_TEST] Available keys: ${jsonData.keys.toList()}');
          }
        } catch (e) {
          print('🧪 [AUTOSUGGEST_TEST] JSON parsing failed: $e');
        }
      }
    } catch (e) {
      print('🧪 [AUTOSUGGEST_TEST] Request failed: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _client.close();
  }
}

/// Custom exception for autosuggest API errors
class AutosuggestException implements Exception {
  final String message;
  final int? statusCode;
  final bool isTimeout;
  final bool isNetworkError;
  final bool isFormatError;

  AutosuggestException(
    this.message, {
    this.statusCode,
    this.isTimeout = false,
    this.isNetworkError = false,
    this.isFormatError = false,
  });

  @override
  String toString() => 'AutosuggestException: $message';
}
