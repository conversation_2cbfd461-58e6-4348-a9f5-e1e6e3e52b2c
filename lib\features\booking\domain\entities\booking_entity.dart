import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/utils/enums.dart';

/// Booking entity representing the business model for bookings
class BookingEntity extends Equatable {
  final String? bookingId;
  final String? hotelId;
  final String? hotelName;
  final String? roomId;
  final String? roomType;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int? numberOfGuests;
  final int? numberOfRooms;
  final int? numberOfNights;
  final double? totalAmount;
  final String? currency;
  final BookingStatus? status;
  final PaymentStatus? paymentStatus;
  final DateTime? bookingDate;
  final DateTime? cancellationDate;
  final GuestDetailsEntity? guestDetails;
  final PaymentDetailsEntity? paymentDetails;
  final String? specialRequests;
  final String? confirmationNumber;
  final bool? isBreakfastIncluded;
  final String? cancellationPolicy;

  const BookingEntity({
    this.bookingId,
    this.hotelId,
    this.hotelName,
    this.roomId,
    this.roomType,
    this.checkInDate,
    this.checkOutDate,
    this.numberOfGuests,
    this.numberOfRooms,
    this.numberOfNights,
    this.totalAmount,
    this.currency,
    this.status,
    this.paymentStatus,
    this.bookingDate,
    this.cancellationDate,
    this.guestDetails,
    this.paymentDetails,
    this.specialRequests,
    this.confirmationNumber,
    this.isBreakfastIncluded,
    this.cancellationPolicy,
  });

  @override
  List<Object?> get props => [
        bookingId,
        hotelId,
        hotelName,
        roomId,
        roomType,
        checkInDate,
        checkOutDate,
        numberOfGuests,
        numberOfRooms,
        numberOfNights,
        totalAmount,
        currency,
        status,
        paymentStatus,
        bookingDate,
        cancellationDate,
        guestDetails,
        paymentDetails,
        specialRequests,
        confirmationNumber,
        isBreakfastIncluded,
        cancellationPolicy,
      ];

  /// Copy with method for creating modified instances
  BookingEntity copyWith({
    String? bookingId,
    String? hotelId,
    String? hotelName,
    String? roomId,
    String? roomType,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? numberOfGuests,
    int? numberOfRooms,
    int? numberOfNights,
    double? totalAmount,
    String? currency,
    BookingStatus? status,
    PaymentStatus? paymentStatus,
    DateTime? bookingDate,
    DateTime? cancellationDate,
    GuestDetailsEntity? guestDetails,
    PaymentDetailsEntity? paymentDetails,
    String? specialRequests,
    String? confirmationNumber,
    bool? isBreakfastIncluded,
    String? cancellationPolicy,
  }) {
    return BookingEntity(
      bookingId: bookingId ?? this.bookingId,
      hotelId: hotelId ?? this.hotelId,
      hotelName: hotelName ?? this.hotelName,
      roomId: roomId ?? this.roomId,
      roomType: roomType ?? this.roomType,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      numberOfGuests: numberOfGuests ?? this.numberOfGuests,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      numberOfNights: numberOfNights ?? this.numberOfNights,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      bookingDate: bookingDate ?? this.bookingDate,
      cancellationDate: cancellationDate ?? this.cancellationDate,
      guestDetails: guestDetails ?? this.guestDetails,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      specialRequests: specialRequests ?? this.specialRequests,
      confirmationNumber: confirmationNumber ?? this.confirmationNumber,
      isBreakfastIncluded: isBreakfastIncluded ?? this.isBreakfastIncluded,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
    );
  }
}

/// Guest details entity
class GuestDetailsEntity extends Equatable {
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? nationality;
  final DateTime? dateOfBirth;
  final String? passportNumber;
  final String? specialRequests;

  const GuestDetailsEntity({
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.nationality,
    this.dateOfBirth,
    this.passportNumber,
    this.specialRequests,
  });

  @override
  List<Object?> get props => [
        firstName,
        lastName,
        email,
        phone,
        nationality,
        dateOfBirth,
        passportNumber,
        specialRequests,
      ];
}

/// Payment details entity
class PaymentDetailsEntity extends Equatable {
  final String? paymentId;
  final PaymentStatus? status;
  final String? paymentMethod;
  final double? amount;
  final String? currency;
  final DateTime? paymentDate;
  final String? transactionId;
  final String? cardLastFourDigits;
  final String? paymentGateway;

  const PaymentDetailsEntity({
    this.paymentId,
    this.status,
    this.paymentMethod,
    this.amount,
    this.currency,
    this.paymentDate,
    this.transactionId,
    this.cardLastFourDigits,
    this.paymentGateway,
  });

  @override
  List<Object?> get props => [
        paymentId,
        status,
        paymentMethod,
        amount,
        currency,
        paymentDate,
        transactionId,
        cardLastFourDigits,
        paymentGateway,
      ];
}
