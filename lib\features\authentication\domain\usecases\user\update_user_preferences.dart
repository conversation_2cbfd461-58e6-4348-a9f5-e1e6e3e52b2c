﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/user_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for updating user preferences
class UpdateUserPreferences implements UseCase<UserPreferencesEntity, UpdateUserPreferencesParams> {
  final UserRepository repository;

  UpdateUserPreferences(this.repository);

  @override
  Future<Either<Failure, UserPreferencesEntity>> call(UpdateUserPreferencesParams params) async {
    return await repository.updateUserPreferences(
      language: params.language,
      currency: params.currency,
      emailNotifications: params.emailNotifications,
      pushNotifications: params.pushNotifications,
      smsNotifications: params.smsNotifications,
    );
  }
}

/// Parameters for UpdateUserPreferences use case
class UpdateUserPreferencesParams extends Equatable {
  final String? language;
  final String? currency;
  final bool? emailNotifications;
  final bool? pushNotifications;
  final bool? smsNotifications;

  const UpdateUserPreferencesParams({
    this.language,
    this.currency,
    this.emailNotifications,
    this.pushNotifications,
    this.smsNotifications,
  });

  @override
  List<Object?> get props => [
    language,
    currency,
    emailNotifications,
    pushNotifications,
    smsNotifications,
  ];
}

