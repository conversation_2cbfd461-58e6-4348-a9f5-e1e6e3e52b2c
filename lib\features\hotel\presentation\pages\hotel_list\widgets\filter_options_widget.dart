// import 'package:flutter/material.dart';
// import 'package:kind_ali/core/constants/app_colors.dart';

// class FilterOptionsWidget extends StatefulWidget {
//   const FilterOptionsWidget({super.key});

//   @override
//   State<FilterOptionsWidget> createState() => _FilterOptionsWidgetState();
// }

// class _FilterOptionsWidgetState extends State<FilterOptionsWidget> {
//   // Budget range values
//   RangeValues _currentRangeValues = const RangeValues(5499, 8899);

//   // Bedroom and bathroom counters
//   int _bedroomCount = 1;
//   int _bathroomCount = 1;

//   // Selected filters maps with counts
//   Map<String, int> accommodationTypes = {
//     'Resort': 4,
//     'Hotel': 1,
//   };

//   Map<String, int> amenities = {
//     'Swimming Pool': 2,
//     'Bar & Lounge': 1,
//     'Conference Room': 1,
//     'Fitness Center': 1,
//     'Live Music': 1,
//   };

//   Map<String, int> propertyTypes = {
//     'Private Suite': 1,
//     'Deluxe Suite': 1,
//     'Studio Apartment': 1,
//     'Executive Suite': 1,
//     'Cottage': 1,
//   };

//   Map<String, int> propertyRatings = {
//     '3 Stars': 2,
//     '4 Stars': 2,
//     '5 Stars': 1,
//   };

//   Map<String, int> facilities = {
//     'Free Breakfast': 1,
//     'Yoga Center': 1,
//     'Sea View': 1,
//     'Free WiFi': 1,
//     'Free Airport Shuttle': 1,
//     'Gaming Zone': 1,
//     'Private Beach': 1,
//     'Luxury Spa': 1,
//     'Sunset View': 1,
//     'Jacuzzi': 1,
//   };

//   // Selected filter tracking
//   Map<String, bool> selectedAccommodations = {};
//   Map<String, bool> selectedAmenities = {};
//   Map<String, bool> selectedPropertyTypes = {};
//   Map<String, bool> selectedRatings = {};
//   Map<String, bool> selectedFacilities = {};

//   @override
//   void initState() {
//     super.initState();
//     // Initialize all selected maps to false
//     for (var key in accommodationTypes.keys) {
//       selectedAccommodations[key] = false;
//     }
//     for (var key in amenities.keys) {
//       selectedAmenities[key] = false;
//     }
//     for (var key in propertyTypes.keys) {
//       selectedPropertyTypes[key] = false;
//     }
//     for (var key in propertyRatings.keys) {
//       selectedRatings[key] = false;
//     }
//     for (var key in facilities.keys) {
//       selectedFacilities[key] = false;
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: AppColors.primary,
//         shape: const RoundedRectangleBorder(
//           borderRadius: BorderRadius.vertical(
//             bottom: Radius.circular(20),
//           ),
//         ),
//         title: const Text(
//           'Filter Options',
//           style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
//         ),
//         leading: InkWell(
//           onTap: () {
//             Navigator.pop(context);
//           },
//           child: const Icon(Icons.close, color: Colors.white),
//         ),
//         actions: [
//           TextButton(
//             onPressed: () {
//               // Reset all filters
//               setState(() {
//                 for (var key in selectedAccommodations.keys) {
//                   selectedAccommodations[key] = false;
//                 }
//                 for (var key in selectedAmenities.keys) {
//                   selectedAmenities[key] = false;
//                 }
//                 for (var key in selectedPropertyTypes.keys) {
//                   selectedPropertyTypes[key] = false;
//                 }
//                 for (var key in selectedRatings.keys) {
//                   selectedRatings[key] = false;
//                 }
//                 for (var key in selectedFacilities.keys) {
//                   selectedFacilities[key] = false;
//                 }
//                 _currentRangeValues = const RangeValues(5499, 8899);
//                 _bedroomCount = 1;
//                 _bathroomCount = 1;
//               });
//             },
//             child: const Text(
//               'Reset',
//               style: TextStyle(color: Colors.white),
//             ),
//           ),
//         ],
//       ),
//       body: Column(
//         children: [
//           Expanded(
//             child: ListView(
//               padding: const EdgeInsets.all(16),
//               children: [
//                 // Accommodation Types
//                 _buildSectionHeader('Accommodation Types'),
//                 _buildFilterChips(accommodationTypes, selectedAccommodations),
//                 const SizedBox(height: 20),

//                 // Amenities
//                 _buildSectionHeader('Amenities'),
//                 _buildFilterChips(amenities, selectedAmenities),
//                 const SizedBox(height: 20),

//                 // Budget Range
//                 _buildSectionHeader('Your Budget (per night)'),
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 16),
//                   child: Column(
//                     children: [
//                       RangeSlider(
//                         values: _currentRangeValues,
//                         min: 5000,
//                         max: 10000,
//                         divisions: 50,
//                         labels: RangeLabels(
//                           '₹${_currentRangeValues.start.round()}',
//                           '₹${_currentRangeValues.end.round()}',
//                         ),
//                         onChanged: (RangeValues values) {
//                           setState(() {
//                             _currentRangeValues = values;
//                           });
//                         },
//                         activeColor: AppColors.primary,
//                         inactiveColor: AppColors.primary.withOpacity(0.2),
//                       ),
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           Text('₹${_currentRangeValues.start.round()}'),
//                           Text('₹${_currentRangeValues.end.round()}'),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ),
//                 const SizedBox(height: 20),

//                 // Bedrooms and Bathrooms
//                 _buildSectionHeader('Bedrooms and Bathrooms'),
//                 const SizedBox(height: 12),
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 16),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           const Text(
//                             'Bedrooms',
//                             style: TextStyle(fontWeight: FontWeight.w500),
//                           ),
//                           Row(
//                             children: [
//                               _buildCounterButton(
//                                 icon: Icons.remove,
//                                 onPressed: () {
//                                   if (_bedroomCount > 1) {
//                                     setState(() {
//                                       _bedroomCount--;
//                                     });
//                                   }
//                                 },
//                               ),
//                               Padding(
//                                 padding:
//                                     const EdgeInsets.symmetric(horizontal: 16),
//                                 child: Text(
//                                   '$_bedroomCount',
//                                   style: const TextStyle(
//                                       fontSize: 16,
//                                       fontWeight: FontWeight.bold),
//                                 ),
//                               ),
//                               _buildCounterButton(
//                                 icon: Icons.add,
//                                 onPressed: () {
//                                   setState(() {
//                                     _bedroomCount++;
//                                   });
//                                 },
//                               ),
//                             ],
//                           ),
//                         ],
//                       ),
//                       const SizedBox(height: 16),
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           const Text(
//                             'Bathrooms',
//                             style: TextStyle(fontWeight: FontWeight.w500),
//                           ),
//                           Row(
//                             children: [
//                               _buildCounterButton(
//                                 icon: Icons.remove,
//                                 onPressed: () {
//                                   if (_bathroomCount > 1) {
//                                     setState(() {
//                                       _bathroomCount--;
//                                     });
//                                   }
//                                 },
//                               ),
//                               Padding(
//                                 padding:
//                                     const EdgeInsets.symmetric(horizontal: 16),
//                                 child: Text(
//                                   '$_bathroomCount',
//                                   style: const TextStyle(
//                                       fontSize: 16,
//                                       fontWeight: FontWeight.bold),
//                                 ),
//                               ),
//                               _buildCounterButton(
//                                 icon: Icons.add,
//                                 onPressed: () {
//                                   setState(() {
//                                     _bathroomCount++;
//                                   });
//                                 },
//                               ),
//                             ],
//                           ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ),

//                 const SizedBox(height: 20),

//                 // Property Types
//                 _buildSectionHeader('Property Types'),
//                 _buildFilterChips(propertyTypes, selectedPropertyTypes),
//                 const SizedBox(height: 20),

//                 // Property Rating
//                 _buildSectionHeader('Property Rating'),
//                 _buildFilterChips(propertyRatings, selectedRatings),
//                 const SizedBox(height: 20),

//                 // Facilities
//                 _buildSectionHeader('Facilities'),
//                 _buildFilterChips(facilities, selectedFacilities),
//                 const SizedBox(height: 20),
//               ],
//             ),
//           ),
//           // Apply Button
//           Container(
//             width: double.infinity,
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.grey.withOpacity(0.3),
//                   spreadRadius: 1,
//                   blurRadius: 5,
//                   offset: const Offset(0, -2),
//                 ),
//               ],
//             ),
//             child: ElevatedButton(
//               onPressed: () {
//                 // Collect and return selected filters
//                 final selectedFilters = {
//                   'accommodationTypes': selectedAccommodations.entries
//                       .where((e) => e.value)
//                       .map((e) => e.key)
//                       .toList(),
//                   'amenities': selectedAmenities.entries
//                       .where((e) => e.value)
//                       .map((e) => e.key)
//                       .toList(),
//                   'budget': {
//                     'min': _currentRangeValues.start.round(),
//                     'max': _currentRangeValues.end.round(),
//                   },
//                   'bedrooms': _bedroomCount,
//                   'bathrooms': _bathroomCount,
//                   'propertyTypes': selectedPropertyTypes.entries
//                       .where((e) => e.value)
//                       .map((e) => e.key)
//                       .toList(),
//                   'propertyRatings': selectedRatings.entries
//                       .where((e) => e.value)
//                       .map((e) => e.key)
//                       .toList(),
//                   'facilities': selectedFacilities.entries
//                       .where((e) => e.value)
//                       .map((e) => e.key)
//                       .toList(),
//                 };

//                 Navigator.pop(context, selectedFilters);
//               },
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: AppColors.primary,
//                 padding: const EdgeInsets.symmetric(vertical: 15),
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(10),
//                 ),
//               ),
//               child: const Text(
//                 'Apply Filters',
//                 style: TextStyle(
//                   color: Colors.white,
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildSectionHeader(String title) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 10),
//       child: Text(
//         title,
//         style: const TextStyle(
//           fontSize: 18,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }

//   Widget _buildFilterChips(
//       Map<String, int> options, Map<String, bool> selectedMap) {
//     return Column(
//       children: options.entries.map((entry) {
//         final isSelected = selectedMap[entry.key] ?? false;

//         return InkWell(
//           onTap: () {
//             setState(() {
//               selectedMap[entry.key] = !isSelected;
//             });
//           },
//           borderRadius: BorderRadius.circular(10),
//           child: Container(
//             margin: const EdgeInsets.symmetric(vertical: 4),
//             padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
//             decoration: BoxDecoration(
//               color: isSelected
//                   ? AppColors.primary.withOpacity(0.05)
//                   : Colors.white,
//               border: Border.all(
//                 color: isSelected ? AppColors.primary : Colors.grey.shade300,
//               ),
//               borderRadius: BorderRadius.circular(10),
//             ),
//             child: Row(
//               children: [
//                 Checkbox(
//                   value: isSelected,
//                   activeColor: AppColors.primary,
//                   onChanged: (bool? value) {
//                     setState(() {
//                       selectedMap[entry.key] = value ?? false;
//                     });
//                   },
//                 ),
//                 Expanded(
//                   child: Text(
//                     entry.key,
//                     style: const TextStyle(fontSize: 14),
//                   ),
//                 ),
//                 Container(
//                   padding:
//                       const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//                   decoration: BoxDecoration(
//                     color: Colors.grey.shade200,
//                     borderRadius: BorderRadius.circular(10),
//                   ),
//                   child: Text(
//                     '${entry.value}',
//                     style: const TextStyle(fontSize: 12),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         );
//       }).toList(),
//     );
//   }

//   Widget _buildCounterButton(
//       {required IconData icon, required VoidCallback onPressed}) {
//     return Container(
//       decoration: BoxDecoration(
//         shape: BoxShape.circle,
//         border: Border.all(color: Colors.grey.shade300),
//       ),
//       child: InkWell(
//         onTap: onPressed,
//         borderRadius: BorderRadius.circular(20),
//         child: Padding(
//           padding: const EdgeInsets.all(6),
//           child: Icon(
//             icon,
//             size: 18,
//             color: AppColors.primary,
//           ),
//         ),
//       ),
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';

class FilterOptionsWidget extends StatefulWidget {
  const FilterOptionsWidget({super.key});

  @override
  State<FilterOptionsWidget> createState() => _FilterOptionsWidgetState();
}

class _FilterOptionsWidgetState extends State<FilterOptionsWidget> {
  // Budget range values
  RangeValues _currentRangeValues = const RangeValues(5499, 8899);

  // Sample data for price distribution (you can replace this with real data)
  final List<int> priceDistribution = [
    2, 5, 8, 12, 18, 25, 30, 28, 22, 15, 10, 8, 5, 3, 2
  ]; // Represents number of hotels in each price range

  // Bedroom and bathroom counters
  int _bedroomCount = 1;
  int _bathroomCount = 1;

  // Selected filters maps with counts
  Map<String, int> accommodationTypes = {
    'Resort': 4,
    'Hotel': 1,
  };

  Map<String, int> amenities = {
    'Swimming Pool': 2,
    'Bar & Lounge': 1,
    'Conference Room': 1,
    'Fitness Center': 1,
    'Live Music': 1,
  };

  Map<String, int> propertyTypes = {
    'Private Suite': 1,
    'Deluxe Suite': 1,
    'Studio Apartment': 1,
    'Executive Suite': 1,
    'Cottage': 1,
  };

  Map<String, int> propertyRatings = {
    '3 Stars': 2,
    '4 Stars': 2,
    '5 Stars': 1,
  };

  Map<String, int> facilities = {
    'Free Breakfast': 1,
    'Yoga Center': 1,
    'Sea View': 1,
    'Free WiFi': 1,
    'Free Airport Shuttle': 1,
    'Gaming Zone': 1,
    'Private Beach': 1,
    'Luxury Spa': 1,
    'Sunset View': 1,
    'Jacuzzi': 1,
  };

  // Selected filter tracking
  Map<String, bool> selectedAccommodations = {};
  Map<String, bool> selectedAmenities = {};
  Map<String, bool> selectedPropertyTypes = {};
  Map<String, bool> selectedRatings = {};
  Map<String, bool> selectedFacilities = {};

  @override
  void initState() {
    super.initState();
    // Initialize all selected maps to false
    for (var key in accommodationTypes.keys) {
      selectedAccommodations[key] = false;
    }
    for (var key in amenities.keys) {
      selectedAmenities[key] = false;
    }
    for (var key in propertyTypes.keys) {
      selectedPropertyTypes[key] = false;
    }
    for (var key in propertyRatings.keys) {
      selectedRatings[key] = false;
    }
    for (var key in facilities.keys) {
      selectedFacilities[key] = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        title: const Text(
          'Filter Options',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        leading: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: const Icon(Icons.close, color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Reset all filters
              setState(() {
                for (var key in selectedAccommodations.keys) {
                  selectedAccommodations[key] = false;
                }
                for (var key in selectedAmenities.keys) {
                  selectedAmenities[key] = false;
                }
                for (var key in selectedPropertyTypes.keys) {
                  selectedPropertyTypes[key] = false;
                }
                for (var key in selectedRatings.keys) {
                  selectedRatings[key] = false;
                }
                for (var key in selectedFacilities.keys) {
                  selectedFacilities[key] = false;
                }
                _currentRangeValues = const RangeValues(5499, 8899);
                _bedroomCount = 1;
                _bathroomCount = 1;
              });
            },
            child: const Text(
              'Reset',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Accommodation Types
                _buildSectionHeader('Accommodation Types'),
                _buildFilterChips(accommodationTypes, selectedAccommodations),
                const SizedBox(height: 20),

                // Amenities
                _buildSectionHeader('Amenities'),
                _buildFilterChips(amenities, selectedAmenities),
                const SizedBox(height: 20),

                // Budget Range with Graph
                _buildSectionHeader('Your Budget (per night)'),
                _buildBudgetRangeWithGraph(),
                const SizedBox(height: 20),

                // Bedrooms and Bathrooms
                _buildSectionHeader('Bedrooms and Bathrooms'),
                const SizedBox(height: 12),
                _buildRoomCounters(),
                const SizedBox(height: 20),

                // Property Types
                _buildSectionHeader('Property Types'),
                _buildFilterChips(propertyTypes, selectedPropertyTypes),
                const SizedBox(height: 20),

                // Property Rating
                _buildSectionHeader('Property Rating'),
                _buildFilterChips(propertyRatings, selectedRatings),
                const SizedBox(height: 20),

                // Facilities
                _buildSectionHeader('Facilities'),
                _buildFilterChips(facilities, selectedFacilities),
                const SizedBox(height: 20),
              ],
            ),
          ),
          // Apply Button
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildBudgetRangeWithGraph() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price distribution graph
          Container(
            height: 80,
            margin: const EdgeInsets.only(bottom: 16),
            child: CustomPaint(
              painter: PriceDistributionPainter(
                data: priceDistribution,
                selectedRange: _currentRangeValues,
                minPrice: 5000,
                maxPrice: 10000,
                primaryColor: AppColors.primary,
              ),
              size: const Size(double.infinity, 80),
            ),
          ),
          
          // Range Slider
          RangeSlider(
            values: _currentRangeValues,
            min: 5000,
            max: 10000,
            divisions: 50,
            labels: RangeLabels(
              '₹${_currentRangeValues.start.round()}',
              '₹${_currentRangeValues.end.round()}',
            ),
            onChanged: (RangeValues values) {
              setState(() {
                _currentRangeValues = values;
              });
            },
            activeColor: AppColors.primary,
            inactiveColor: AppColors.primary.withOpacity(0.2),
          ),
          
          // Price labels and count
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '₹${_currentRangeValues.start.round()}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const Text(
                    'Min Price',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_getHotelsInRange()} hotels',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '₹${_currentRangeValues.end.round()}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const Text(
                    'Max Price',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRoomCounters() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Bedrooms',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              Row(
                children: [
                  _buildCounterButton(
                    icon: Icons.remove,
                    onPressed: () {
                      if (_bedroomCount > 1) {
                        setState(() {
                          _bedroomCount--;
                        });
                      }
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      '$_bedroomCount',
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                  _buildCounterButton(
                    icon: Icons.add,
                    onPressed: () {
                      setState(() {
                        _bedroomCount++;
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Bathrooms',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              Row(
                children: [
                  _buildCounterButton(
                    icon: Icons.remove,
                    onPressed: () {
                      if (_bathroomCount > 1) {
                        setState(() {
                          _bathroomCount--;
                        });
                      }
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      '$_bathroomCount',
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                  _buildCounterButton(
                    icon: Icons.add,
                    onPressed: () {
                      setState(() {
                        _bathroomCount++;
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildApplyButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          // Collect and return selected filters
          final selectedFilters = {
            'accommodationTypes': selectedAccommodations.entries
                .where((e) => e.value)
                .map((e) => e.key)
                .toList(),
            'amenities': selectedAmenities.entries
                .where((e) => e.value)
                .map((e) => e.key)
                .toList(),
            'budget': {
              'min': _currentRangeValues.start.round(),
              'max': _currentRangeValues.end.round(),
            },
            'bedrooms': _bedroomCount,
            'bathrooms': _bathroomCount,
            'propertyTypes': selectedPropertyTypes.entries
                .where((e) => e.value)
                .map((e) => e.key)
                .toList(),
            'propertyRatings': selectedRatings.entries
                .where((e) => e.value)
                .map((e) => e.key)
                .toList(),
            'facilities': selectedFacilities.entries
                .where((e) => e.value)
                .map((e) => e.key)
                .toList(),
          };

          Navigator.pop(context, selectedFilters);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          padding: const EdgeInsets.symmetric(vertical: 15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: const Text(
          'Apply Filters',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  int _getHotelsInRange() {
    // Calculate how many hotels are in the selected price range
    const double minPrice = 5000;
    const double maxPrice = 10000;
    final double pricePerBin = (maxPrice - minPrice) / priceDistribution.length;
    
    int startBin = (((_currentRangeValues.start - minPrice) / pricePerBin).floor()).clamp(0, priceDistribution.length - 1);
    int endBin = (((_currentRangeValues.end - minPrice) / pricePerBin).ceil()).clamp(0, priceDistribution.length - 1);
    
    int totalHotels = 0;
    for (int i = startBin; i <= endBin; i++) {
      totalHotels += priceDistribution[i];
    }
    
    return totalHotels;
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildFilterChips(
      Map<String, int> options, Map<String, bool> selectedMap) {
    return Column(
      children: options.entries.map((entry) {
        final isSelected = selectedMap[entry.key] ?? false;

        return InkWell(
          onTap: () {
            setState(() {
              selectedMap[entry.key] = !isSelected;
            });
          },
          borderRadius: BorderRadius.circular(10),
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 4),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.primary.withOpacity(0.05)
                  : Colors.white,
              border: Border.all(
                color: isSelected ? AppColors.primary : Colors.grey.shade300,
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: isSelected,
                  activeColor: AppColors.primary,
                  onChanged: (bool? value) {
                    setState(() {
                      selectedMap[entry.key] = value ?? false;
                    });
                  },
                ),
                Expanded(
                  child: Text(
                    entry.key,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${entry.value}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCounterButton(
      {required IconData icon, required VoidCallback onPressed}) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(6),
          child: Icon(
            icon,
            size: 18,
            color: AppColors.primary,
          ),
        ),
      ),
    );
  }
}

// Custom painter for the price distribution graph
class PriceDistributionPainter extends CustomPainter {
  final List<int> data;
  final RangeValues selectedRange;
  final double minPrice;
  final double maxPrice;
  final Color primaryColor;

  PriceDistributionPainter({
    required this.data,
    required this.selectedRange,
    required this.minPrice,
    required this.maxPrice,
    required this.primaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..style = PaintingStyle.fill;

    final selectedPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    final unselectedPaint = Paint()
      ..color = primaryColor.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final double barWidth = size.width / data.length;
    final int maxValue = data.reduce((a, b) => a > b ? a : b);
    
    if (maxValue == 0) return;

    final double pricePerBin = (maxPrice - minPrice) / data.length;

    for (int i = 0; i < data.length; i++) {
      final double barHeight = (data[i] / maxValue) * size.height * 0.8;
      final double x = i * barWidth;
      final double y = size.height - barHeight;

      // Calculate the price range for this bar
      final double binStartPrice = minPrice + (i * pricePerBin);
      final double binEndPrice = binStartPrice + pricePerBin;

      // Check if this bar is within the selected range
      final bool isInRange = !(binEndPrice < selectedRange.start || binStartPrice > selectedRange.end);

      final rect = Rect.fromLTWH(
        x + 1, // Small gap between bars
        y,
        barWidth - 2, // Small gap between bars
        barHeight,
      );

      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(2)),
        isInRange ? selectedPaint : unselectedPaint,
      );
    }

    // Draw grid lines
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..strokeWidth = 0.5;

    for (int i = 1; i <= 4; i++) {
      final double y = size.height * (i / 5);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}