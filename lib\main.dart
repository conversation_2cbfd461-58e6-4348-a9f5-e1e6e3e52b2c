import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_localizations.dart';
import 'package:kind_ali/shared/presentation/providers/localization_notifier.dart';
import 'package:kind_ali/core/routes/app_routes.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the localization provider for language changes
    final localizationState = ref.watch(localizationProvider);

    return MaterialApp(
      title: 'kind_ali',
      debugShowCheckedModeBanner: false,

      // Localization setup with Riverpod integration
      locale: localizationState.locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocalizationNotifier.supportedLocales,

      builder: (context, child) {
        // Apply text direction based on current locale
        return Directionality(
          textDirection: localizationState.textDirection,
          child: child!,
        );
      },

          theme: ThemeData(
            primarySwatch: AppColors.primaryMaterialColor,
            primaryColor: AppColors.primary,
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              secondary: AppColors.secondary,
              tertiary: AppColors.tertiary,
              surface: AppColors.surface,
              error: AppColors.error,
              onPrimary: AppColors.textOnPrimary,
              onSecondary: AppColors.textOnSecondary,
              onSurface: AppColors.text,
              onError: Colors.white,
              brightness: Brightness.light,
            ),
            scaffoldBackgroundColor: AppColors.background,
            appBarTheme: AppBarTheme(
              backgroundColor: AppColors.secondary,
              foregroundColor: Colors.white,
              elevation: 0,
            ),
            cardTheme: CardTheme(
              color: AppColors.surface,
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            buttonTheme: ButtonThemeData(
              buttonColor: AppColors.secondary,
              textTheme: ButtonTextTheme.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 2,
                shadowColor: AppColors.shadow,
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.secondary,
              ),
            ),
            outlinedButtonTheme: OutlinedButtonThemeData(
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.secondary,
                side: BorderSide(color: AppColors.secondary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            textTheme: TextTheme(
              displayLarge: TextStyle(color: AppColors.text),
              displayMedium: TextStyle(color: AppColors.text),
              displaySmall: TextStyle(color: AppColors.text),
              headlineLarge: TextStyle(color: AppColors.text),
              headlineMedium: TextStyle(color: AppColors.text),
              headlineSmall: TextStyle(color: AppColors.text),
              titleLarge: TextStyle(color: AppColors.text),
              titleMedium: TextStyle(color: AppColors.text),
              titleSmall: TextStyle(color: AppColors.text),
              bodyLarge: TextStyle(color: AppColors.text),
              bodyMedium: TextStyle(color: AppColors.text),
              bodySmall: TextStyle(color: AppColors.textLight),
              labelLarge: TextStyle(color: AppColors.text),
              labelMedium: TextStyle(color: AppColors.text),
              labelSmall: TextStyle(color: AppColors.textLight),
            ),
            dividerTheme: DividerThemeData(
              color: AppColors.divider,
              thickness: 1,
            ),
            brightness: Brightness.light,
          ),

          // DARK MODE DISABLED - Commented out to prevent dark mode activation
          // darkTheme: ThemeData(
          //   primarySwatch: AppColors.primaryMaterialColor,
          //   primaryColor: AppColors.primary,
          //   colorScheme: ColorScheme.dark(
          //     primary: AppColors.primary,
          //     secondary: AppColors.secondary,
          //     tertiary: AppColors.tertiary,
          //     surface: Color(0xFF101036),
          //     error: AppColors.error,
          //     onPrimary: Colors.white,
          //     onSecondary: Colors.white,
          //     onSurface: Colors.white,
          //     onError: Colors.white,
          //     brightness: Brightness.dark,
          //   ),
          //   scaffoldBackgroundColor: Color(0xFF0A0A2A),
          //   appBarTheme: AppBarTheme(
          //     backgroundColor: AppColors.secondary,
          //     foregroundColor: Colors.white,
          //     elevation: 0,
          //   ),
          //   brightness: Brightness.dark,
          // ),

          // Force light mode only - prevents any dark mode activation
          themeMode: ThemeMode.light,

      initialRoute: AppRoutes.splash,
      routes: AppRoutes.getRoutes(),
      onGenerateRoute: AppRoutes.generateRoute,
    );
  }
}
