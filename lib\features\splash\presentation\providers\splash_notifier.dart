/// State notifier for splash screen functionality
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/app_initialization.dart';
import '../../data/providers/splash_providers.dart';

/// State for splash screen
class SplashState {
  final bool isLoading;
  final bool isInitialized;
  final AppInitialization? initializationData;
  final String? error;
  final String? navigationRoute;

  const SplashState({
    this.isLoading = true,
    this.isInitialized = false,
    this.initializationData,
    this.error,
    this.navigationRoute,
  });

  SplashState copyWith({
    bool? isLoading,
    bool? isInitialized,
    AppInitialization? initializationData,
    String? error,
    String? navigationRoute,
  }) {
    return SplashState(
      isLoading: isLoading ?? this.isLoading,
      isInitialized: isInitialized ?? this.isInitialized,
      initializationData: initializationData ?? this.initializationData,
      error: error,
      navigationRoute: navigationRoute ?? this.navigationRoute,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SplashState &&
        other.isLoading == isLoading &&
        other.isInitialized == isInitialized &&
        other.initializationData == initializationData &&
        other.error == error &&
        other.navigationRoute == navigationRoute;
  }

  @override
  int get hashCode => Object.hash(
        isLoading,
        isInitialized,
        initializationData,
        error,
        navigationRoute,
      );
}

/// State notifier for splash screen
class SplashNotifier extends StateNotifier<SplashState> {
  final Ref _ref;

  SplashNotifier(this._ref) : super(const SplashState());

  /// Initialize the app and determine navigation route
  Future<void> initializeApp() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Get the initialize app data use case
      final initializeAppData = await _ref.read(initializeAppDataProvider.future);

      // Initialize app data
      final initData = await initializeAppData();

      // Determine navigation route
      final route = initData.getNextRoute();

      state = state.copyWith(
        isLoading: false,
        isInitialized: true,
        initializationData: initData,
        navigationRoute: route,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isInitialized: false,
        error: e.toString(),
        navigationRoute: '/dashboard', // Default fallback
      );
    }
  }

  /// Reset splash state
  void reset() {
    state = const SplashState();
  }

  /// Get the navigation route
  String getNavigationRoute() {
    return state.navigationRoute ?? '/dashboard';
  }

  /// Check if initialization is complete
  bool get isInitializationComplete => state.isInitialized && !state.isLoading;

  /// Check if there's an error
  bool get hasError => state.error != null;
}

/// Provider for splash state notifier
final splashNotifierProvider = StateNotifierProvider<SplashNotifier, SplashState>((ref) {
  // We'll handle the async initialization inside the notifier
  return SplashNotifier(ref);
});
