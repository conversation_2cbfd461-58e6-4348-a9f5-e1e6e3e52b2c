import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:kind_ali/core/constants/api_constants.dart';
import 'package:kind_ali/core/error/exceptions.dart';
import 'package:kind_ali/features/authentication/data/models/auth_model.dart';
import 'package:kind_ali/features/authentication/data/models/user_model.dart';

/// Abstract class for authentication remote data source
abstract class AuthRemoteDataSource {
  /// Login with email and password
  Future<AuthModel> loginWithEmail({
    required String email,
    required String password,
  });
  
  /// Login with phone and OTP
  Future<AuthModel> loginWithPhone({
    required String phone,
    required String otp,
  });
  
  /// Register with email
  Future<AuthModel> registerWithEmail({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  });
  
  /// Register with phone
  Future<AuthModel> registerWithPhone({
    required String firstName,
    required String lastName,
    required String phone,
  });
  
  /// Send OTP to phone
  Future<void> sendOtp(String phone);
  
  /// Verify OTP
  Future<bool> verifyOtp({
    required String phone,
    required String otp,
  });
  
  /// Refresh authentication token
  Future<AuthModel> refreshToken(String refreshToken);
  
  /// Logout user
  Future<void> logout(String accessToken);

  /// Forgot password
  Future<void> forgotPassword(String email);

  /// Reset password
  Future<void> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  });
}

/// Implementation of authentication remote data source
/// NOTE: This is currently stubbed as no API is available yet
/// When API becomes available, implement actual HTTP calls here
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final http.Client client;
  
  AuthRemoteDataSourceImpl({required this.client});

  @override
  Future<AuthModel> loginWithEmail({
    required String email,
    required String password,
  }) async {
    // TODO: Implement when API is available
    // For now, throw exception to fall back to local authentication
    throw ServerException('API not available - using local authentication');
    
    /* Future implementation when API is ready:
    try {
      final response = await client.post(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.login}'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
        body: json.encode({
          'email': email,
          'password': password,
        }),
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return AuthModel.fromJson(jsonData);
      } else {
        throw ServerException('Login failed: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
    */
  }

  @override
  Future<AuthModel> loginWithPhone({
    required String phone,
    required String otp,
  }) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local authentication');
  }

  @override
  Future<AuthModel> registerWithEmail({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  }) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local authentication');
  }

  @override
  Future<AuthModel> registerWithPhone({
    required String firstName,
    required String lastName,
    required String phone,
  }) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local authentication');
  }

  @override
  Future<void> sendOtp(String phone) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local OTP simulation');
  }

  @override
  Future<bool> verifyOtp({
    required String phone,
    required String otp,
  }) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local OTP verification');
  }

  @override
  Future<AuthModel> refreshToken(String refreshToken) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local token management');
  }

  @override
  Future<void> logout(String accessToken) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local logout');
  }

  @override
  Future<void> forgotPassword(String email) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local forgot password simulation');
  }

  @override
  Future<void> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    // TODO: Implement when API is available
    throw ServerException('API not available - using local reset password simulation');
  }
}
