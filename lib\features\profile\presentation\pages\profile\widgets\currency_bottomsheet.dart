import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';
import 'package:kind_ali/core/constants/app_localizations.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/shared/presentation/providers/currency_notifier.dart';

class CurrencyBottomsheet extends ConsumerWidget {
  const CurrencyBottomsheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<Map<String, String>> currencies = [
      {'code': 'USD', 'name': 'US Dollar', 'symbol': '\$', 'flag': '🇺🇸'},
      {'code': 'EUR', 'name': 'Euro', 'symbol': '€', 'flag': '🇪🇺'},
      {'code': 'GBP', 'name': 'British Pound', 'symbol': '£', 'flag': '🇬🇧'},
      {'code': 'JPY', 'name': 'Japanese Yen', 'symbol': '¥', 'flag': '🇯🇵'},
      {'code': 'AUD', 'name': 'Australian Dollar', 'symbol': 'A\$', 'flag': '🇦🇺'},
      {'code': 'CAD', 'name': 'Canadian Dollar', 'symbol': 'C\$', 'flag': '🇨🇦'},
      {'code': 'CHF', 'name': 'Swiss Franc', 'symbol': 'CHF', 'flag': '🇨🇭'},
      {'code': 'CNY', 'name': 'Chinese Yuan', 'symbol': '¥', 'flag': '🇨🇳'},
      {'code': 'INR', 'name': 'Indian Rupee', 'symbol': '₹', 'flag': '🇮🇳'},
      {'code': 'AED', 'name': 'UAE Dirham', 'symbol': 'د.إ', 'flag': '🇦🇪'},
      {'code': 'SAR', 'name': 'Saudi Riyal', 'symbol': 'ر.س', 'flag': '🇸🇦'},
      {'code': 'KWD', 'name': 'Kuwaiti Dinar', 'symbol': 'د.ك', 'flag': '🇰🇼'},
      {'code': 'QAR', 'name': 'Qatari Riyal', 'symbol': 'ر.ق', 'flag': '🇶🇦'},
      {'code': 'OMR', 'name': 'Omani Rial', 'symbol': 'ر.ع.', 'flag': '🇴🇲'},
      {'code': 'BHD', 'name': 'Bahraini Dinar', 'symbol': '.د.ب', 'flag': '🇧🇭'},
      {'code': 'SGD', 'name': 'Singapore Dollar', 'symbol': 'S\$', 'flag': '🇸🇬'},
      {'code': 'HKD', 'name': 'Hong Kong Dollar', 'symbol': 'HK\$', 'flag': '🇭🇰'},
      {'code': 'KRW', 'name': 'South Korean Won', 'symbol': '₩', 'flag': '🇰🇷'},
      {'code': 'THB', 'name': 'Thai Baht', 'symbol': '฿', 'flag': '🇹🇭'},
      {'code': 'MYR', 'name': 'Malaysian Ringgit', 'symbol': 'RM', 'flag': '🇲🇾'},
    ];

    final currencyState = ref.watch(currencyProvider);
    final currentCurrency = currencyState.selectedCurrency;

        return Container(
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
            boxShadow: [
              BoxShadow(
                color: AppColors.neutralDark.withAlpha(30),
                blurRadius: 20,
                spreadRadius: 0,
                offset: const Offset(0, -4),
              ),
            ],
          ),
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + AppDimensions.paddingSizeSmall,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle indicator
              Center(
                child: Container(
                  margin: EdgeInsets.only(
                    top: AppDimensions.paddingSizeSmall,
                    bottom: AppDimensions.paddingSizeExtraSmall,
                  ),
                  width: 32,
                  height: 3,
                  decoration: BoxDecoration(
                    color: AppColors.divider,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),

              // Title Section - Reduced padding
              Padding(
                padding: EdgeInsets.fromLTRB(
                  AppDimensions.paddingSizeDefault,
                  AppDimensions.paddingSizeSmall,
                  AppDimensions.paddingSizeDefault,
                  AppDimensions.paddingSizeExtraSmall,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(AppDimensions.paddingSizeExtraSmall),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(25),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            Icons.monetization_on_rounded,
                            color: AppColors.primary,
                            size: 20,
                          ),
                        ),
                        SizedBox(width: AppDimensions.paddingSizeSmall),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                               'settings.currency'.tr,
                                style: AppTextStyles.headline2.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.neutralDark,
                                  fontSize: 18,
                                ),
                              ),
                              SizedBox(height: 2),
                              Text('settings.chooseCurrency'.tr,
                                style: AppTextStyles.bodyText2.copyWith(
                                  color: AppColors.textLight,
                                  fontSize: 13,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Currency Grid - Reduced container size
              Flexible(
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.45,
                  ),
                  margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingSizeSmall),
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const BouncingScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: MediaQuery.of(context).size.width > 400 ? 4 : 3,
                      crossAxisSpacing: AppDimensions.paddingSizeExtraSmall,
                      mainAxisSpacing: AppDimensions.paddingSizeExtraSmall,
                      childAspectRatio: 1.0,
                    ),
                    itemCount: currencies.length,
                    itemBuilder: (context, index) {
                      final currency = currencies[index];
                      final isSelected = currentCurrency == currency['code'];

                      return _CurrencyGridCard(
                        currency: currency,
                        isSelected: isSelected,
                        onTap: () {
                          ref.read(currencyProvider.notifier).setCurrency(currency['code']!);
                          Navigator.pop(context);

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                '${'profile.currencyChangedTo'.tr} ${currency['name']} (${currency['symbol']})',
                                style: AppTextStyles.bodyText1.copyWith(color: Colors.white),
                              ),
                              duration: const Duration(seconds: 2),
                              behavior: SnackBarBehavior.floating,
                              backgroundColor: AppColors.primary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              margin: EdgeInsets.all(AppDimensions.paddingSizeDefault),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),

              SizedBox(height: AppDimensions.paddingSizeDefault),

              // Close button - Reduced padding
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingSizeDefault),
                child: SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingSizeSmall),
                      backgroundColor: AppColors.neutralLight,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(14),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)?.translate('common.close') ?? 'Close',
                      style: AppTextStyles.bodyText1.copyWith(
                        color: AppColors.textLight,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),

              SizedBox(height: AppDimensions.paddingSizeSmall),
            ],
          ),
        );
  }
}

class _CurrencyGridCard extends StatefulWidget {
  final Map<String, String> currency;
  final bool isSelected;
  final VoidCallback onTap;

  const _CurrencyGridCard({
    required this.currency,
    required this.isSelected,
    required this.onTap,
  });

  @override
  State<_CurrencyGridCard> createState() => _CurrencyGridCardState();
}

class _CurrencyGridCardState extends State<_CurrencyGridCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _controller.forward(),
            onTapUp: (_) => _controller.reverse(),
            onTapCancel: () => _controller.reverse(),
            onTap: widget.onTap,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? AppColors.primary
                    : AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: widget.isSelected
                      ? AppColors.primary
                      : AppColors.divider,
                  width: widget.isSelected ? 0 : 0.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.isSelected
                        ? AppColors.primary.withAlpha(25)
                        : AppColors.neutralDark.withAlpha(5),
                    blurRadius: widget.isSelected ? 6 : 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(AppDimensions.paddingSizeExtraSmall),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Flag - Reduced size
                    Text(
                      widget.currency['flag']!,
                      style: const TextStyle(fontSize: 16),
                    ),
                    
                    SizedBox(height: 2),

                    // Currency Code - Reduced font size
                    Text(
                      widget.currency['code']!,
                      style: AppTextStyles.bodyText1.copyWith(
                        fontWeight: FontWeight.w700,
                        color: widget.isSelected
                            ? Colors.white
                            : AppColors.neutralDark,
                        fontSize: 12,
                      ),
                    ),

                    // Currency Symbol - Reduced font size
                    Text(
                      widget.currency['symbol']!,
                      style: AppTextStyles.caption.copyWith(
                        fontWeight: FontWeight.w500,
                        color: widget.isSelected
                            ? Colors.white.withOpacity(0.8)
                            : AppColors.textLight,
                        fontSize: 9,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}