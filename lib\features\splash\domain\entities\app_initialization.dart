/// Entity representing app initialization state
library;

/// Entity for app initialization information
class AppInitialization {
  /// Whether user has completed onboarding
  final bool hasCompletedOnboarding;
  
  /// Whether user is currently logged in
  final bool isUserLoggedIn;
  
  /// Current app version
  final String appVersion;
  
  /// Last time the app was launched
  final DateTime lastLaunchTime;
  
  /// Whether user needs to complete profile setup
  final bool needsProfileCompletion;
  
  /// The route to navigate to after splash
  final String? navigationRoute;

  const AppInitialization({
    required this.hasCompletedOnboarding,
    required this.isUserLoggedIn,
    required this.appVersion,
    required this.lastLaunchTime,
    this.needsProfileCompletion = false,
    this.navigationRoute,
  });

  /// Determine the next navigation route based on app state
  String getNextRoute() {
    if (!hasCompletedOnboarding) {
      return '/onboarding';
    }
    
    if (!isUserLoggedIn) {
      return '/dashboard'; // Show dashboard with login prompt
    }
    
    if (needsProfileCompletion) {
      return '/progressive-onboarding';
    }
    
    return navigationRoute ?? '/dashboard';
  }

  /// Check if app needs initialization
  bool get needsInitialization => !hasCompletedOnboarding || !isUserLoggedIn;

  /// Check if user should see onboarding
  bool get shouldShowOnboarding => !hasCompletedOnboarding;

  /// Check if user should see login
  bool get shouldShowLogin => !isUserLoggedIn && hasCompletedOnboarding;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppInitialization &&
        other.hasCompletedOnboarding == hasCompletedOnboarding &&
        other.isUserLoggedIn == isUserLoggedIn &&
        other.appVersion == appVersion &&
        other.lastLaunchTime == lastLaunchTime &&
        other.needsProfileCompletion == needsProfileCompletion &&
        other.navigationRoute == navigationRoute;
  }

  @override
  int get hashCode => Object.hash(
        hasCompletedOnboarding,
        isUserLoggedIn,
        appVersion,
        lastLaunchTime,
        needsProfileCompletion,
        navigationRoute,
      );

  @override
  String toString() {
    return 'AppInitialization(hasCompletedOnboarding: $hasCompletedOnboarding, isUserLoggedIn: $isUserLoggedIn, appVersion: $appVersion, lastLaunchTime: $lastLaunchTime, needsProfileCompletion: $needsProfileCompletion, navigationRoute: $navigationRoute)';
  }
}
