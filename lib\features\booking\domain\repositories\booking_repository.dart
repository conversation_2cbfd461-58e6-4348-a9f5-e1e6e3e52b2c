﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../entities/booking_entity.dart';

/// Abstract repository for booking-related operations
abstract class BookingRepository {
  /// Create a new booking
  Future<Either<Failure, BookingEntity>> createBooking({
    required String hotelId,
    required String roomId,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int numberOfGuests,
    required int numberOfRooms,
    required GuestDetailsEntity guestDetails,
    String? specialRequests,
  });

  /// Get booking details by ID
  Future<Either<Failure, BookingEntity>> getBookingDetails(String bookingId);

  /// Get user's booking history
  Future<Either<Failure, List<BookingEntity>>> getBookingHistory();

  /// Cancel booking
  Future<Either<Failure, void>> cancelBooking(String bookingId);

  /// Update booking
  Future<Either<Failure, BookingEntity>> updateBooking({
    required String bookingId,
    GuestDetailsEntity? guestDetails,
    String? specialRequests,
  });

  /// Get upcoming bookings
  Future<Either<Failure, List<BookingEntity>>> getUpcomingBookings();

  /// Get past bookings
  Future<Either<Failure, List<BookingEntity>>> getPastBookings();

  /// Get cancelled bookings
  Future<Either<Failure, List<BookingEntity>>> getCancelledBookings();

  /// Check booking availability
  Future<Either<Failure, bool>> checkAvailability({
    required String hotelId,
    required String roomId,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int numberOfRooms,
  });

  /// Calculate booking price
  Future<Either<Failure, double>> calculateBookingPrice({
    required String hotelId,
    required String roomId,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int numberOfRooms,
  });
}
