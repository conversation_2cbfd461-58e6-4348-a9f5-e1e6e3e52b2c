class Hotel {
  Data? data;

  Hotel({this.data});

  Hotel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  Result? result;

  Data({this.result});

  Data.fromJson(Map<String, dynamic> json) {
    result =
        json['result'] != null ? new Result.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.result != null) {
      data['result'] = this.result!.toJson();
    }
    return data;
  }
}

class Result {
  List<InventoryInfoList>? inventoryInfoList;

  Result({this.inventoryInfoList});

  Result.fromJson(Map<String, dynamic> json) {
    if (json['inventoryInfoList'] != null) {
      inventoryInfoList = <InventoryInfoList>[];
      json['inventoryInfoList'].forEach((v) {
        inventoryInfoList!.add(new InventoryInfoList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.inventoryInfoList != null) {
      data['inventoryInfoList'] =
          this.inventoryInfoList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class InventoryInfoList {
  int? locationId;
  int? hotelId;
  String? name;
  String? locality;
  String? city;
  String? userRating;
  String? userRatingCategory;
  num? starRating;
  int? userRatingCount;
  List<ImageInfoList>? imageInfoList;
  List<RoomDetails>? roomDetails;
 num? comfortRating;
  num? taxesAndCharges;
  String? accommodationType;
  List<String>? topOfferings;
  int? roomsCountLeft;
  String? distanceFromSearchedEntity;
  List<FomoTags>? fomoTags;
  List<Amenities>? amenities;
  GeoLocationInfo? geoLocationInfo;
  FareDetail? fareDetail;

  InventoryInfoList(
      {this.locationId,
      this.hotelId,
      this.name,
      this.locality,
      this.city,
      this.userRating,
      this.userRatingCategory,
      this.starRating,
      this.userRatingCount,
      this.imageInfoList,
      this.roomDetails,
      this.comfortRating,
      this.taxesAndCharges,
      this.accommodationType,
      this.topOfferings,
      this.roomsCountLeft,
      this.distanceFromSearchedEntity,
      this.fomoTags,
      this.amenities,
      this.geoLocationInfo,
      this.fareDetail});

  InventoryInfoList.fromJson(Map<String, dynamic> json) {
    locationId = json['locationId'];
    hotelId = json['hotelId'];
    name = json['name'];
    locality = json['locality'];
    city = json['city'];
    userRating = json['userRating'];
    userRatingCategory = json['userRatingCategory'];
    starRating = json['starRating']?.toDouble();

    userRatingCount = json['userRatingCount'];
    if (json['imageInfoList'] != null) {
      imageInfoList = <ImageInfoList>[];
      json['imageInfoList'].forEach((v) {
        imageInfoList!.add(new ImageInfoList.fromJson(v));
      });
    }
    if (json['roomDetails'] != null) {
      roomDetails = <RoomDetails>[];
      json['roomDetails'].forEach((v) {
        roomDetails!.add(new RoomDetails.fromJson(v));
      });
    }
    comfortRating = json['comfortRating'];
    taxesAndCharges = json['taxesAndCharges'];
    accommodationType = json['accommodationType'];
    topOfferings = json['topOfferings'].cast<String>();
    roomsCountLeft = json['roomsCountLeft'];
    distanceFromSearchedEntity = json['distanceFromSearchedEntity'];
    if (json['fomoTags'] != null) {
      fomoTags = <FomoTags>[];
      json['fomoTags'].forEach((v) {
        fomoTags!.add(new FomoTags.fromJson(v));
      });
    }
    if (json['amenities'] != null) {
      amenities = <Amenities>[];
      json['amenities'].forEach((v) {
        amenities!.add(new Amenities.fromJson(v));
      });
    }
    geoLocationInfo = json['geoLocationInfo'] != null
        ? new GeoLocationInfo.fromJson(json['geoLocationInfo'])
        : null;
    fareDetail = json['fareDetail'] != null
        ? new FareDetail.fromJson(json['fareDetail'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['locationId'] = this.locationId;
    data['hotelId'] = this.hotelId;
    data['name'] = this.name;
    data['locality'] = this.locality;
    data['city'] = this.city;
    data['userRating'] = this.userRating;
    data['userRatingCategory'] = this.userRatingCategory;
    data['starRating'] = this.starRating;
    data['userRatingCount'] = this.userRatingCount;
    if (this.imageInfoList != null) {
      data['imageInfoList'] =
          this.imageInfoList!.map((v) => v.toJson()).toList();
    }
    if (this.roomDetails != null) {
      data['roomDetails'] = this.roomDetails!.map((v) => v.toJson()).toList();
    }
    data['comfortRating'] = this.comfortRating;
    data['taxesAndCharges'] = this.taxesAndCharges;
    data['accommodationType'] = this.accommodationType;
    data['topOfferings'] = this.topOfferings;
    data['roomsCountLeft'] = this.roomsCountLeft;
    data['distanceFromSearchedEntity'] = this.distanceFromSearchedEntity;
    if (this.fomoTags != null) {
      data['fomoTags'] = this.fomoTags!.map((v) => v.toJson()).toList();
    }
    if (this.amenities != null) {
      data['amenities'] = this.amenities!.map((v) => v.toJson()).toList();
    }
    if (this.geoLocationInfo != null) {
      data['geoLocationInfo'] = this.geoLocationInfo!.toJson();
    }
    if (this.fareDetail != null) {
      data['fareDetail'] = this.fareDetail!.toJson();
    }
    return data;
  }
}

class ImageInfoList {
  String? pictureId;
  String? url;
  String? caption;
  String? imageCategory;
  int? rank;

  ImageInfoList(
      {this.pictureId, this.url, this.caption, this.imageCategory, this.rank});

  ImageInfoList.fromJson(Map<String, dynamic> json) {
    pictureId = json['pictureId'];
    url = json['url'];
    caption = json['caption'];
    imageCategory = json['imageCategory'];
    rank = json['rank'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['pictureId'] = this.pictureId;
    data['url'] = this.url;
    data['caption'] = this.caption;
    data['imageCategory'] = this.imageCategory;
    data['rank'] = this.rank;
    return data;
  }
}

class RoomDetails {
  String? type;
  int? bedroom;
  int? livingRoom;
  int? bathroom;
  String? size;
  String? bed;

  RoomDetails(
      {this.type,
      this.bedroom,
      this.livingRoom,
      this.bathroom,
      this.size,
      this.bed});

  RoomDetails.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    bedroom = json['bedroom'];
    livingRoom = json['livingRoom'];
    bathroom = json['bathroom'];
    size = json['size'];
    bed = json['bed'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    data['bedroom'] = this.bedroom;
    data['livingRoom'] = this.livingRoom;
    data['bathroom'] = this.bathroom;
    data['size'] = this.size;
    data['bed'] = this.bed;
    return data;
  }
}

class FomoTags {
  String? data;
  String? fomoType;

  FomoTags({this.data, this.fomoType});

  FomoTags.fromJson(Map<String, dynamic> json) {
    data = json['data'];
    fomoType = json['fomoType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['data'] = this.data;
    data['fomoType'] = this.fomoType;
    return data;
  }
}

class Amenities {
  String? name;
  String? amenityUrl;

  Amenities({this.name, this.amenityUrl});

  Amenities.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    amenityUrl = json['amenityUrl'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['amenityUrl'] = this.amenityUrl;
    return data;
  }
}

class GeoLocationInfo {
  double? lat;
  double? lon;

  GeoLocationInfo({this.lat, this.lon});

  GeoLocationInfo.fromJson(Map<String, dynamic> json) {
    lat = json['lat'];
    lon = json['lon'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['lat'] = this.lat;
    data['lon'] = this.lon;
    return data;
  }
}

class FareDetail {
 double? displayedBaseFare;
 String? offerPercentage;
    double? offerPrice;
  double? totalPrice;

  FareDetail({this.displayedBaseFare, this.totalPrice, this.offerPercentage, this.offerPrice});

  FareDetail.fromJson(Map<String, dynamic> json) {
    displayedBaseFare = json['displayedBaseFare']?.toDouble();
    totalPrice = json['totalPrice']?.toDouble();
    offerPercentage = json['markupDiscountPercent']; // Map from markupDiscountPercent
    offerPrice = json['markUpFare']?.toDouble(); // Map from markUpFare as offer price
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['displayedBaseFare'] = this.displayedBaseFare;
    data['offerPercentage'] = this.offerPercentage;
    data['offerPrice'] = this.offerPrice;
    data['totalPrice'] = this.totalPrice;
    return data;
  }
}
