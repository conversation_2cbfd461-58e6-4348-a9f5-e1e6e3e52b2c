/// Local data source for splash screen initialization
library;

import 'package:shared_preferences/shared_preferences.dart';

/// Abstract class defining splash local data source contract
abstract class SplashLocalDataSource {
  /// Check if user has completed onboarding
  Future<bool> hasCompletedOnboarding();
  
  /// Check if user is logged in
  Future<bool> isUserLoggedIn();
  
  /// Get app initialization data
  Future<Map<String, dynamic>> getAppInitializationData();
  
  /// Set onboarding completion status
  Future<void> setOnboardingCompleted(bool completed);
}

/// Implementation of splash local data source
class SplashLocalDataSourceImpl implements SplashLocalDataSource {
  final SharedPreferences _prefs;
  
  // Keys for shared preferences
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _userLoggedInKey = 'user_logged_in';
  static const String _appVersionKey = 'app_version';
  
  SplashLocalDataSourceImpl(this._prefs);
  
  @override
  Future<bool> hasCompletedOnboarding() async {
    return _prefs.getBool(_onboardingCompletedKey) ?? false;
  }
  
  @override
  Future<bool> isUserLoggedIn() async {
    return _prefs.getBool(_userLoggedInKey) ?? false;
  }
  
  @override
  Future<Map<String, dynamic>> getAppInitializationData() async {
    return {
      'hasCompletedOnboarding': await hasCompletedOnboarding(),
      'isUserLoggedIn': await isUserLoggedIn(),
      'appVersion': _prefs.getString(_appVersionKey) ?? '1.0.0',
      'lastLaunchTime': DateTime.now().toIso8601String(),
    };
  }
  
  @override
  Future<void> setOnboardingCompleted(bool completed) async {
    await _prefs.setBool(_onboardingCompletedKey, completed);
  }
}
