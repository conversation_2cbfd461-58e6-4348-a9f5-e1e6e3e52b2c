﻿import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../../core/error/exceptions.dart';
import 'package:kind_ali/features/search/data/models/search_cities.dart';

/// Abstract class for search local data source
abstract class SearchLocalDataSource {
  /// Get cities from local JSON assets
  Future<List<SearchCities>> getCitiesFromAssets();
  
  /// Get cached cities
  Future<List<SearchCities>> getCachedCities();
  
  /// Cache cities data
  Future<void> cacheCities(List<SearchCities> cities);
  
  /// Get recent searches
  Future<List<String>> getRecentSearches();
  
  /// Add to recent searches
  Future<void> addToRecentSearches(String searchQuery);
  
  /// Clear recent searches
  Future<void> clearRecentSearches();
  
  /// Check if cities cache is valid
  Future<bool> isCitiesCacheValid();
  
  /// Clear cities cache
  Future<void> clearCitiesCache();
}

/// Implementation of search local data source
class SearchLocalDataSourceImpl implements SearchLocalDataSource {
  final SharedPreferences sharedPreferences;
  
  SearchLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<List<SearchCities>> getCitiesFromAssets() async {
    try {
      final String response = await rootBundle.loadString(
        '${AppConstants.jsonPath}${AppConstants.citiesJsonFile}',
      );
      final List<dynamic> jsonData = json.decode(response);
      return jsonData.map((e) => SearchCities.fromJson(e)).toList();
    } catch (e) {
      throw FileSystemException('Failed to load cities from assets: $e');
    }
  }

  @override
  Future<List<SearchCities>> getCachedCities() async {
    try {
      final jsonString = sharedPreferences.getString(AppConstants.keyCachedCities);
      if (jsonString != null) {
        final List<dynamic> jsonData = json.decode(jsonString);
        return jsonData.map((e) => SearchCities.fromJson(e)).toList();
      }
      throw CacheException('No cities found in cache');
    } catch (e) {
      throw CacheException('Failed to get cities from cache: $e');
    }
  }

  @override
  Future<void> cacheCities(List<SearchCities> cities) async {
    try {
      final jsonString = json.encode(cities.map((e) => e.toJson()).toList());
      await sharedPreferences.setString(AppConstants.keyCachedCities, jsonString);
      
      // Store cache timestamp
      await sharedPreferences.setInt(
        '${AppConstants.keyCachedCities}_timestamp',
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      throw CacheException('Failed to cache cities: $e');
    }
  }

  @override
  Future<List<String>> getRecentSearches() async {
    try {
      final recentSearches = sharedPreferences.getStringList(AppConstants.keyRecentSearches);
      return recentSearches ?? [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> addToRecentSearches(String searchQuery) async {
    try {
      final recentSearches = await getRecentSearches();
      
      // Remove if already exists to avoid duplicates
      recentSearches.remove(searchQuery);
      
      // Add to beginning
      recentSearches.insert(0, searchQuery);
      
      // Keep only the latest searches (max limit)
      if (recentSearches.length > AppConstants.maxRecentSearches) {
        recentSearches.removeRange(AppConstants.maxRecentSearches, recentSearches.length);
      }
      
      await sharedPreferences.setStringList(AppConstants.keyRecentSearches, recentSearches);
    } catch (e) {
      throw CacheException('Failed to add to recent searches: $e');
    }
  }

  @override
  Future<void> clearRecentSearches() async {
    try {
      await sharedPreferences.remove(AppConstants.keyRecentSearches);
    } catch (e) {
      throw CacheException('Failed to clear recent searches: $e');
    }
  }

  @override
  Future<bool> isCitiesCacheValid() async {
    try {
      final timestamp = sharedPreferences.getInt('${AppConstants.keyCachedCities}_timestamp');
      if (timestamp == null) return false;
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime).inHours;
      
      return difference < AppConstants.cacheValidityDuration;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> clearCitiesCache() async {
    try {
      await sharedPreferences.remove(AppConstants.keyCachedCities);
      await sharedPreferences.remove('${AppConstants.keyCachedCities}_timestamp');
    } catch (e) {
      throw CacheException('Failed to clear cities cache: $e');
    }
  }
}
