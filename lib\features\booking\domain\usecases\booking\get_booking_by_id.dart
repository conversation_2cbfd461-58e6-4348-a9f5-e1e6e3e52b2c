﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/booking_entity.dart';
import '../../repositories/booking_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for getting booking by ID
class GetBookingById implements UseCase<BookingEntity, GetBookingByIdParams> {
  final BookingRepository repository;

  GetBookingById(this.repository);

  @override
  Future<Either<Failure, BookingEntity>> call(GetBookingByIdParams params) async {
    return await repository.getBookingDetails(params.bookingId);
  }
}

/// Parameters for GetBookingById use case
class GetBookingByIdParams extends Equatable {
  final String bookingId;

  const GetBookingByIdParams({required this.bookingId});

  @override
  List<Object> get props => [bookingId];
}

