/// Data model for app initialization information
library;

import '../../domain/entities/app_initialization.dart';

/// Data model for app initialization
class AppInitializationModel extends AppInitialization {
  const AppInitializationModel({
    required super.hasCompletedOnboarding,
    required super.isUserLoggedIn,
    required super.appVersion,
    required super.lastLaunchTime,
    super.needsProfileCompletion = false,
    super.navigationRoute,
  });

  /// Create from JSON
  factory AppInitializationModel.fromJson(Map<String, dynamic> json) {
    return AppInitializationModel(
      hasCompletedOnboarding: json['hasCompletedOnboarding'] ?? false,
      isUserLoggedIn: json['isUserLoggedIn'] ?? false,
      appVersion: json['appVersion'] ?? '1.0.0',
      lastLaunchTime: DateTime.parse(json['lastLaunchTime'] ?? DateTime.now().toIso8601String()),
      needsProfileCompletion: json['needsProfileCompletion'] ?? false,
      navigationRoute: json['navigationRoute'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'hasCompletedOnboarding': hasCompletedOnboarding,
      'isUserLoggedIn': isUserLoggedIn,
      'appVersion': appVersion,
      'lastLaunchTime': lastLaunchTime.toIso8601String(),
      'needsProfileCompletion': needsProfileCompletion,
      'navigationRoute': navigationRoute,
    };
  }

  /// Create a copy with updated values
  AppInitializationModel copyWith({
    bool? hasCompletedOnboarding,
    bool? isUserLoggedIn,
    String? appVersion,
    DateTime? lastLaunchTime,
    bool? needsProfileCompletion,
    String? navigationRoute,
  }) {
    return AppInitializationModel(
      hasCompletedOnboarding: hasCompletedOnboarding ?? this.hasCompletedOnboarding,
      isUserLoggedIn: isUserLoggedIn ?? this.isUserLoggedIn,
      appVersion: appVersion ?? this.appVersion,
      lastLaunchTime: lastLaunchTime ?? this.lastLaunchTime,
      needsProfileCompletion: needsProfileCompletion ?? this.needsProfileCompletion,
      navigationRoute: navigationRoute ?? this.navigationRoute,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppInitializationModel &&
        other.hasCompletedOnboarding == hasCompletedOnboarding &&
        other.isUserLoggedIn == isUserLoggedIn &&
        other.appVersion == appVersion &&
        other.lastLaunchTime == lastLaunchTime &&
        other.needsProfileCompletion == needsProfileCompletion &&
        other.navigationRoute == navigationRoute;
  }

  @override
  int get hashCode => Object.hash(
        hasCompletedOnboarding,
        isUserLoggedIn,
        appVersion,
        lastLaunchTime,
        needsProfileCompletion,
        navigationRoute,
      );

  @override
  String toString() {
    return 'AppInitializationModel(hasCompletedOnboarding: $hasCompletedOnboarding, isUserLoggedIn: $isUserLoggedIn, appVersion: $appVersion, lastLaunchTime: $lastLaunchTime, needsProfileCompletion: $needsProfileCompletion, navigationRoute: $navigationRoute)';
  }
}
