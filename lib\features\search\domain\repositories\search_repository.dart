﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/features/search/domain/entities/search_entity.dart';

/// Abstract repository for search-related operations
abstract class SearchRepository {
  /// Get all cities
  Future<Either<Failure, List<CityEntity>>> getCities();

  /// Search cities by query
  Future<Either<Failure, List<CityEntity>>> searchCities(String query);

  /// Get popular destinations
  Future<Either<Failure, List<PopularPlaceEntity>>> getPopularDestinations();

  /// Get popular cities
  Future<Either<Failure, List<CityEntity>>> getPopularCities();

  /// Get recent searches
  Future<Either<Failure, List<String>>> getRecentSearches();

  /// Add to recent searches
  Future<Either<Failure, void>> addToRecentSearches(String searchQuery);

  /// Clear recent searches
  Future<Either<Failure, void>> clearRecentSearches();

  /// Get current location
  Future<Either<Failure, String>> getCurrentLocation();

  /// Get nearby cities
  Future<Either<Failure, List<CityEntity>>> getNearbyCities({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
  });
}
