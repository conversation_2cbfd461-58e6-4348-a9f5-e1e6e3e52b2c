import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/shared/widgets/safe_consumer_widget.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/features/onboarding/presentation/widgets/welcome_card.dart';
import 'package:kind_ali/features/onboarding/presentation/widgets/name_collection_card.dart';
import 'package:kind_ali/features/onboarding/presentation/widgets/contact_info_card.dart';
import 'package:kind_ali/features/onboarding/presentation/widgets/completion_celebration_card.dart';
import 'package:kind_ali/core/routes/app_routes.dart';

class ProgressiveOnboardingScreen extends SafeConsumerStatefulWidget {
  final String? source;
  final VoidCallback? onBookingContinue;
  const ProgressiveOnboardingScreen({super.key, this.source, this.onBookingContinue});

  @override
  SafeConsumerState<ProgressiveOnboardingScreen> createState() => _ProgressiveOnboardingScreenState();
}

class _ProgressiveOnboardingScreenState extends SafeConsumerState<ProgressiveOnboardingScreen>
    with TickerProviderStateMixin {
  
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  int _currentStep = 0;
  final int _totalSteps = 4; // Welcome, Name, Contact, Celebration
  
  // User data collection
  String _userName = '';
  String _userEmail = '';
  String _userPhone = '';
  
  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _animationController.forward();
    _loadUserData();
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }
  
  // Load existing user data based on login method
  void _loadUserData() async {
    final authState = ref.read(authProvider);
    final authNotifier = ref.read(authProvider.notifier);
    final storedData = await authNotifier.getStoredProfileData();
    
    safeSetState(() {
      _userName = storedData['name'] ?? '';
      if (authState.loginMethod == 'email') {
        _userEmail = authState.currentUserEmail ?? '';
        _userPhone = '';
      } else if (authState.loginMethod == 'phone') {
        _userPhone = authState.currentUserPhone ?? '';
        _userEmail = '';
      }
    });
  }
  
  // Navigate to next step
  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      safeSetState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
  
  // Navigate to previous step
  void _previousStep() {
    if (_currentStep > 0) {
      safeSetState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
  
  // Complete onboarding
  void _completeOnboarding() async {
    try {
      // Save all collected data
      final authNotifier = ref.read(authProvider.notifier);
      // Save to SharedPreferences
      await _saveUserData();
      // Mark profile as complete
      await authNotifier.markProfileAsComplete();
      if (mounted) {
        bool found = false;
        Navigator.of(context).popUntil((route) {
          if (route.settings.name == AppRoutes.booking) {
            found = true;
            return true;
          }
          return false;
        });
        if (!found) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            AppRoutes.dashboard,
            (route) => false,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing onboarding: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  // Save user data to SharedPreferences
  Future<void> _saveUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save user data
      await prefs.setString('user_name', _userName);

      if (_userEmail.isNotEmpty) {
        await prefs.setString('user_email', _userEmail);
      }

      if (_userPhone.isNotEmpty) {
        await prefs.setString('user_phone', _userPhone);
      }

      debugPrint('Onboarding data saved: Name=$_userName, Email=$_userEmail, Phone=$_userPhone');
    } catch (e) {
      debugPrint('Error saving onboarding data: $e');
      rethrow;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            
            // Main content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(), // Disable swipe
                onPageChanged: (index) {
                  safeSetState(() {
                    _currentStep = index;
                  });
                },
                children: [
                  // Step 1: Welcome
                  WelcomeCard(
                    onContinue: _nextStep,
                  ),
                  
                  // Step 2: Name Collection
                  NameCollectionCard(
                    initialName: _userName,
                    onNameChanged: (name) => _userName = name,
                    onContinue: _nextStep,
                    onBack: _previousStep,
                  ),
                  
                  // Step 3: Contact Info
                  ContactInfoCard(
                    initialEmail: _userEmail,
                    initialPhone: _userPhone,
                    loginMethod: ref.read(authProvider).loginMethod ?? 'email',
                    onEmailChanged: (email) => _userEmail = email,
                    onPhoneChanged: (phone) => _userPhone = phone,
                    onContinue: _nextStep,
                    onBack: _previousStep,
                  ),
                  
                  // Step 4: Celebration
                  CompletionCelebrationCard(
                    userName: _userName,
                    onComplete: _completeOnboarding,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Build progress indicator
  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: List.generate(_totalSteps, (index) {
          final isActive = index <= _currentStep;
          final isCompleted = index < _currentStep;
          
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
              height: 4,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: isActive 
                    ? AppColors.primary 
                    : AppColors.primary.withOpacity(0.2),
              ),
            ),
          );
        }),
      ),
    );
  }
}
