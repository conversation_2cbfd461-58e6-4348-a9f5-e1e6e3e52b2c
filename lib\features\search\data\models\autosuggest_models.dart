/// Autosuggest API response and item models
library;

class AutosuggestResponse {
  final List<AutosuggestItem> suggestions;
  final bool success;
  final String? message;

  AutosuggestResponse({
    required this.suggestions,
    required this.success,
    this.message,
  });

  factory AutosuggestResponse.fromJson(Map<String, dynamic> json) {
    print('🔧 [AUTOSUGGEST_MODEL] Parsing JSON: $json');

    // Try different possible response structures
    List<dynamic>? suggestionsList;

    // Option 1: Nested data.locationSuggestions (your actual API format)
    if (json['data'] != null && json['data']['locationSuggestions'] != null) {
      suggestionsList = json['data']['locationSuggestions'] as List<dynamic>?;
      print('📋 [AUTOSUGGEST_MODEL] Found locationSuggestions in data wrapper with ${suggestionsList?.length ?? 0} items');
    }
    // Option 2: Direct locationSuggestions
    else if (json['locationSuggestions'] != null) {
      suggestionsList = json['locationSuggestions'] as List<dynamic>?;
      print('📋 [AUTOSUGGEST_MODEL] Found direct locationSuggestions array with ${suggestionsList?.length ?? 0} items');
    }
    // Option 3: Direct suggestions array
    else if (json['suggestions'] != null) {
      suggestionsList = json['suggestions'] as List<dynamic>?;
      print('📋 [AUTOSUGGEST_MODEL] Found suggestions array with ${suggestionsList?.length ?? 0} items');
    }
    // Option 4: Data wrapper with suggestions
    else if (json['data'] != null && json['data']['suggestions'] != null) {
      suggestionsList = json['data']['suggestions'] as List<dynamic>?;
      print('📋 [AUTOSUGGEST_MODEL] Found suggestions in data wrapper with ${suggestionsList?.length ?? 0} items');
    }
    // Option 5: Results array
    else if (json['results'] != null) {
      suggestionsList = json['results'] as List<dynamic>?;
      print('📋 [AUTOSUGGEST_MODEL] Found results array with ${suggestionsList?.length ?? 0} items');
    }
    // Option 6: Items array
    else if (json['items'] != null) {
      suggestionsList = json['items'] as List<dynamic>?;
      print('📋 [AUTOSUGGEST_MODEL] Found items array with ${suggestionsList?.length ?? 0} items');
    }
    // Option 7: Direct array (if response is just an array)
    else if (json is List) {
      suggestionsList = json as List<dynamic>;
      print('📋 [AUTOSUGGEST_MODEL] Response is direct array with ${suggestionsList.length} items');
    }

    final suggestions = suggestionsList
        ?.map((item) {
          try {
            return AutosuggestItem.fromJson(item);
          } catch (e) {
            print('⚠️ [AUTOSUGGEST_MODEL] Failed to parse suggestion item: $item, error: $e');
            return null;
          }
        })
        .where((item) => item != null)
        .cast<AutosuggestItem>()
        .toList() ?? [];

    print('✅ [AUTOSUGGEST_MODEL] Successfully parsed ${suggestions.length} suggestions');

    return AutosuggestResponse(
      suggestions: suggestions,
      success: json['success'] ?? (suggestions.isNotEmpty),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'suggestions': suggestions.map((item) => item.toJson()).toList(),
      'success': success,
      'message': message,
    };
  }
}

class AutosuggestItem {
  final String id;
  final String name;
  final String type;
  final String? description;
  final String? countryCode;
  final String? country;
  final int? propertyCount;
  final double? latitude;
  final double? longitude;

  AutosuggestItem({
    required this.id,
    required this.name,
    required this.type,
    this.description,
    this.countryCode,
    this.country,
    this.propertyCount,
    this.latitude,
    this.longitude,
  });

  factory AutosuggestItem.fromJson(Map<String, dynamic> json) {
    print('🔧 [AUTOSUGGEST_ITEM] Parsing item: $json');

    // Parse fields based on your API response format
    final id = json['id']?.toString() ?? '';

    final name = json['name'] ?? '';

    final type = json['type'] ?? 'city';

    // Use fullName as description if available, otherwise use name
    final description = json['fullName'] ?? json['name'] ?? '';

    final country = json['country'] ?? '';

    final countryCode = json['country'] ?? '';

    // Extract coordinates from nested coordinates object
    final coordinates = json['coordinates'] as Map<String, dynamic>?;
    final latitude = coordinates?['lat']?.toDouble();
    final longitude = coordinates?['long']?.toDouble();

    // For hotels, we can use referenceScore as a property indicator
    final propertyCount = json['referenceScore'] != null ? 1 : null;

    print('✅ [AUTOSUGGEST_ITEM] Parsed: $name ($type) - $country');

    return AutosuggestItem(
      id: id,
      name: name,
      type: type,
      description: description,
      countryCode: countryCode,
      country: country,
      propertyCount: propertyCount,
      latitude: latitude,
      longitude: longitude,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'description': description,
      'countryCode': countryCode,
      'country': country,
      'propertyCount': propertyCount,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AutosuggestItem &&
        other.id == id &&
        other.name == name &&
        other.type == type &&
        other.description == description &&
        other.countryCode == countryCode &&
        other.country == country &&
        other.propertyCount == propertyCount &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode => Object.hash(
        id,
        name,
        type,
        description,
        countryCode,
        country,
        propertyCount,
        latitude,
        longitude,
      );

  @override
  String toString() {
    return 'AutosuggestItem(id: $id, name: $name, type: $type, country: $country)';
  }
}
