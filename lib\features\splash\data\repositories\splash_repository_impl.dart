/// Implementation of splash repository
library;

import '../../domain/entities/app_initialization.dart';
import '../../domain/repositories/splash_repository.dart';
import '../datasources/splash_local_datasource.dart';
import '../models/app_initialization_model.dart';

/// Implementation of splash repository
class SplashRepositoryImpl implements SplashRepository {
  final SplashLocalDataSource _localDataSource;

  SplashRepositoryImpl({
    required SplashLocalDataSource localDataSource,
  }) : _localDataSource = localDataSource;

  @override
  Future<AppInitialization> getAppInitializationData() async {
    try {
      final data = await _localDataSource.getAppInitializationData();
      return AppInitializationModel.fromJson(data);
    } catch (e) {
      // Return default initialization data on error
      return AppInitializationModel(
        hasCompletedOnboarding: false,
        isUserLoggedIn: false,
        appVersion: '1.0.0',
        lastLaunchTime: DateTime.now(),
      );
    }
  }

  @override
  Future<bool> hasCompletedOnboarding() async {
    try {
      return await _localDataSource.hasCompletedOnboarding();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> isUserLoggedIn() async {
    try {
      return await _localDataSource.isUserLoggedIn();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> setOnboardingCompleted(bool completed) async {
    try {
      await _localDataSource.setOnboardingCompleted(completed);
    } catch (e) {
      // Handle error silently for now
      // In production, you might want to log this error
    }
  }

  @override
  Future<void> initializeApp() async {
    try {
      // Perform any app initialization tasks
      // This could include:
      // - Loading essential data
      // - Setting up analytics
      // - Checking for app updates
      // - Initializing crash reporting
      
      // For now, just ensure we have basic data
      await getAppInitializationData();
    } catch (e) {
      // Handle initialization errors
      // In production, you might want to show an error dialog
      rethrow;
    }
  }
}
