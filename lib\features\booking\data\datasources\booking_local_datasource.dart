import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/core/error/exceptions.dart';
import 'package:kind_ali/features/booking/data/models/booking_model.dart';

/// Abstract class for booking local data source
abstract class BookingLocalDataSource {
  /// Save booking data
  Future<void> saveBooking(BookingModel booking);
  
  /// Get all bookings
  Future<List<BookingModel>> getAllBookings();
  
  /// Get booking by ID
  Future<BookingModel?> getBookingById(String bookingId);
  
  /// Update booking
  Future<void> updateBooking(BookingModel booking);
  
  /// Delete booking
  Future<void> deleteBooking(String bookingId);
  
  /// Get bookings by status
  Future<List<BookingModel>> getBookingsByStatus(String status);
  
  /// Clear all bookings
  Future<void> clearAllBookings();
}

/// Implementation of booking local data source
class BookingLocalDataSourceImpl implements BookingLocalDataSource {
  final SharedPreferences sharedPreferences;
  static const String _bookingsKey = 'cached_bookings';
  
  BookingLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> saveBooking(BookingModel booking) async {
    try {
      final bookings = await getAllBookings();
      
      // Check if booking already exists
      final existingIndex = bookings.indexWhere((b) => b.bookingId == booking.bookingId);
      
      if (existingIndex != -1) {
        // Update existing booking
        bookings[existingIndex] = booking;
      } else {
        // Add new booking
        bookings.add(booking);
      }
      
      await _saveBookings(bookings);
    } catch (e) {
      throw CacheException('Failed to save booking: $e');
    }
  }

  @override
  Future<List<BookingModel>> getAllBookings() async {
    try {
      final jsonString = sharedPreferences.getString(_bookingsKey);
      if (jsonString != null) {
        final List<dynamic> jsonData = json.decode(jsonString);
        return jsonData.map((e) => BookingModel.fromJson(e)).toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<BookingModel?> getBookingById(String bookingId) async {
    try {
      final bookings = await getAllBookings();
      return bookings.firstWhere(
        (booking) => booking.bookingId == bookingId,
        orElse: () => throw CacheException('Booking not found'),
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> updateBooking(BookingModel booking) async {
    try {
      await saveBooking(booking); // saveBooking handles both create and update
    } catch (e) {
      throw CacheException('Failed to update booking: $e');
    }
  }

  @override
  Future<void> deleteBooking(String bookingId) async {
    try {
      final bookings = await getAllBookings();
      bookings.removeWhere((booking) => booking.bookingId == bookingId);
      await _saveBookings(bookings);
    } catch (e) {
      throw CacheException('Failed to delete booking: $e');
    }
  }

  @override
  Future<List<BookingModel>> getBookingsByStatus(String status) async {
    try {
      final bookings = await getAllBookings();
      return bookings.where((booking) => 
        booking.status?.toString().split('.').last == status.toLowerCase()
      ).toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> clearAllBookings() async {
    try {
      await sharedPreferences.remove(_bookingsKey);
    } catch (e) {
      throw CacheException('Failed to clear all bookings: $e');
    }
  }

  /// Helper method to save bookings list
  Future<void> _saveBookings(List<BookingModel> bookings) async {
    try {
      final jsonString = json.encode(bookings.map((e) => e.toJson()).toList());
      await sharedPreferences.setString(_bookingsKey, jsonString);
    } catch (e) {
      throw CacheException('Failed to save bookings: $e');
    }
  }
}
