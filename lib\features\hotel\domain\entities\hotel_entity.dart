import 'package:equatable/equatable.dart';

/// Hotel entity representing the business model for hotels
class HotelEntity extends Equatable {
  final int? hotelId;
  final int? locationId;
  final String? name;
  final String? locality;
  final String? city;
  final String? userRating;
  final String? userRatingCategory;
  final num? starRating;
  final int? userRatingCount;
  final List<String>? imageUrls;
  final num? comfortRating;
  final num? taxesAndCharges;
  final String? accommodationType;
  final List<String>? topOfferings;
  final int? roomsCountLeft;
  final String? distanceFromSearchedEntity;
  final List<String>? fomoTags;
  final List<String>? amenities;
  final GeoLocationEntity? geoLocation;
  final FareDetailEntity? fareDetail;

  const HotelEntity({
    this.hotelId,
    this.locationId,
    this.name,
    this.locality,
    this.city,
    this.userRating,
    this.userRatingCategory,
    this.starRating,
    this.userRatingCount,
    this.imageUrls,
    this.comfortRating,
    this.taxesAndCharges,
    this.accommodationType,
    this.topOfferings,
    this.roomsCountLeft,
    this.distanceFromSearchedEntity,
    this.fomoTags,
    this.amenities,
    this.geoLocation,
    this.fareDetail,
  });

  @override
  List<Object?> get props => [
        hotelId,
        locationId,
        name,
        locality,
        city,
        userRating,
        userRatingCategory,
        starRating,
        userRatingCount,
        imageUrls,
        comfortRating,
        taxesAndCharges,
        accommodationType,
        topOfferings,
        roomsCountLeft,
        distanceFromSearchedEntity,
        fomoTags,
        amenities,
        geoLocation,
        fareDetail,
      ];
}

/// Geo location entity
class GeoLocationEntity extends Equatable {
  final double? latitude;
  final double? longitude;

  const GeoLocationEntity({
    this.latitude,
    this.longitude,
  });

  @override
  List<Object?> get props => [latitude, longitude];
}

/// Fare detail entity
class FareDetailEntity extends Equatable {
  final double? basePrice;
  final double? totalPrice;
  final double? taxes;
  final double? discount;
  final String? currency;

  const FareDetailEntity({
    this.basePrice,
    this.totalPrice,
    this.taxes,
    this.discount,
    this.currency,
  });

  @override
  List<Object?> get props => [basePrice, totalPrice, taxes, discount, currency];
}
