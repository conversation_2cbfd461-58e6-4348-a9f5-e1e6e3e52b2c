import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class OverviewTab extends StatelessWidget {
  final bool isExpanded;
  final VoidCallback onExpandToggle;

  const OverviewTab({
    Key? key,
    required this.isExpanded,
    required this.onExpandToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('hotel.detail.about'.tr),
        _buildExpandableText(),
        const SizedBox(height: 16),
        _buildPopularFacilities(context),
        const SizedBox(height: 16),
        _buildQuickFacts(),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildExpandableText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Great care is taken to ensure guests experience comfort through top-notch services and amenities. Stay connected with your associates, as complimentary Wi-Fi is available during your entire visit. To facilitate your arrival and departure, you can pre-book airport transfer service prior to checking in. The resort features a beautiful beachfront location with stunning views of the Arabian Sea.',
            maxLines: isExpanded ? null : 3,
            overflow: isExpanded ? null : TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: onExpandToggle,
            child: Text(
              isExpanded ? 'hotel.detail.readLess'.tr : 'hotel.detail.readMore'.tr,
              style: const TextStyle(
                color: AppColors.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPopularFacilities(BuildContext context) {
    final facilities = [
      {'icon': Icons.pool, 'name': 'hotel.facilities.swimmingPool'.tr},
      {'icon': Icons.beach_access, 'name': 'hotel.facilities.beachAccess'.tr},
      {'icon': Icons.wifi, 'name': 'hotel.facilities.freeWifi'.tr},
      {'icon': Icons.restaurant, 'name': 'hotel.facilities.restaurant'.tr},
      {'icon': Icons.local_bar, 'name': 'hotel.facilities.bar'.tr},
      {'icon': Icons.spa, 'name': 'hotel.facilities.spa'.tr},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'hotel.detail.popularFacilities'.tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 1.2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: facilities.length,
            itemBuilder: (context, index) {
              return Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(13),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      facilities[index]['icon'] as IconData,
                      color: AppColors.secondary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      facilities[index]['name'] as String,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 10),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFacts() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickFactItem('hotel.detail.checkIn'.tr, '13:00'),
            _buildQuickFactItem('hotel.detail.checkOut'.tr, '11:00'),
            _buildQuickFactItem('hotel.detail.yearOpened'.tr, '1947'),
            _buildQuickFactItem('hotel.detail.numberOfRooms'.tr, '19'),
            _buildQuickFactItem('hotel.detail.languagesSpoken'.tr, 'English, Hindi'),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFactItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            '$label:',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textLight,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }
}