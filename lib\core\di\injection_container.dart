import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

// Core
import '../network/network_info.dart';

// Data sources
import '../../features/authentication/data/datasources/auth_local_datasource.dart';
import '../../features/booking/data/datasources/booking_local_datasource.dart';
import '../../features/hotel/data/datasources/hotel_local_datasource.dart';
import '../../features/search/data/datasources/search_local_datasource.dart';
import '../../features/authentication/data/datasources/auth_remote_datasource.dart';
import '../../features/hotel/data/datasources/hotel_remote_datasource.dart';

// Repositories
import '../../features/authentication/data/repositories/auth_repository_impl.dart';
import '../../features/authentication/data/repositories/user_repository_impl.dart';
import '../../features/booking/data/repositories/booking_repository_impl.dart';
import '../../features/hotel/data/repositories/hotel_repository_impl.dart';
import '../../features/search/data/repositories/search_repository_impl.dart';
import '../../features/authentication/domain/repositories/auth_repository.dart';
import '../../features/authentication/domain/repositories/user_repository.dart';
import '../../features/booking/domain/repositories/booking_repository.dart';
import '../../features/hotel/domain/repositories/hotel_repository.dart';
import '../../features/search/domain/repositories/search_repository.dart';

// Use Cases
import '../../features/authentication/domain/usecases/auth/get_current_auth.dart';
import '../../features/authentication/domain/usecases/auth/is_logged_in.dart';
import '../../features/authentication/domain/usecases/auth/login_with_email.dart';
import '../../features/authentication/domain/usecases/auth/login_with_phone.dart';
import '../../features/authentication/domain/usecases/auth/logout.dart';
import '../../features/authentication/domain/usecases/auth/register_with_email.dart';
import '../../features/authentication/domain/usecases/auth/register_with_phone.dart';
import '../../features/authentication/domain/usecases/auth/send_otp.dart';
import '../../features/authentication/domain/usecases/auth/verify_otp.dart';
import '../../features/booking/domain/usecases/booking/cancel_booking.dart';
import '../../features/booking/domain/usecases/booking/check_room_availability.dart';
import '../../features/booking/domain/usecases/booking/create_booking.dart';
import '../../features/booking/domain/usecases/booking/get_booking_by_id.dart';
import '../../features/booking/domain/usecases/booking/get_booking_history.dart';
import '../../features/booking/domain/usecases/booking/get_upcoming_bookings.dart';
import '../../features/hotel/domain/usecases/hotel/get_featured_hotels.dart';
import '../../features/hotel/domain/usecases/hotel/get_hotel_details.dart';
import '../../features/hotel/domain/usecases/hotel/get_hotels.dart';
import '../../features/hotel/domain/usecases/hotel/search_hotels.dart';
import '../../features/search/domain/usecases/search/add_to_recent_searches.dart';
import '../../features/search/domain/usecases/search/get_cities.dart';
import '../../features/search/domain/usecases/search/get_popular_destinations.dart';
import '../../features/search/domain/usecases/search/get_recent_searches.dart';
import '../../features/search/domain/usecases/search/search_cities.dart';
import '../../features/authentication/domain/usecases/user/get_current_user.dart';
import '../../features/authentication/domain/usecases/user/get_user_preferences.dart';
import '../../features/wishlist/domain/usecases/wishlist/manage_wishlist.dart';
import '../../features/authentication/domain/usecases/user/update_user_preferences.dart';
import '../../features/authentication/domain/usecases/user/update_user_profile.dart';

final sl = GetIt.instance;

/// Initialize all dependencies
Future<void> init() async {
  //! Features - Use Cases

  // Hotel Use Cases
  sl.registerLazySingleton(() => GetHotels(sl()));
  sl.registerLazySingleton(() => GetHotelDetails(sl()));
  sl.registerLazySingleton(() => SearchHotels(sl()));
  sl.registerLazySingleton(() => GetFeaturedHotels(sl()));

  // Auth Use Cases
  sl.registerLazySingleton(() => LoginWithEmail(sl()));
  sl.registerLazySingleton(() => LoginWithPhone(sl()));
  sl.registerLazySingleton(() => RegisterWithEmail(sl()));
  sl.registerLazySingleton(() => RegisterWithPhone(sl()));
  sl.registerLazySingleton(() => SendOtp(sl()));
  sl.registerLazySingleton(() => VerifyOtp(sl()));
  sl.registerLazySingleton(() => Logout(sl()));
  sl.registerLazySingleton(() => GetCurrentAuth(sl()));
  sl.registerLazySingleton(() => IsLoggedIn(sl()));

  // Booking Use Cases
  sl.registerLazySingleton(() => CreateBooking(sl()));
  sl.registerLazySingleton(() => GetBookingHistory(sl()));
  sl.registerLazySingleton(() => GetBookingById(sl()));
  sl.registerLazySingleton(() => CancelBooking(sl()));
  sl.registerLazySingleton(() => GetUpcomingBookings(sl()));
  sl.registerLazySingleton(() => CheckRoomAvailability(sl()));

  // Search Use Cases
  sl.registerLazySingleton(() => GetCities(sl()));
  sl.registerLazySingleton(() => SearchCities(sl()));
  sl.registerLazySingleton(() => GetPopularDestinations(sl()));
  sl.registerLazySingleton(() => GetRecentSearches(sl()));
  sl.registerLazySingleton(() => AddToRecentSearches(sl()));

  // User Use Cases
  sl.registerLazySingleton(() => GetCurrentUser(sl()));
  sl.registerLazySingleton(() => UpdateUserProfile(sl()));
  sl.registerLazySingleton(() => GetUserPreferences(sl()));
  sl.registerLazySingleton(() => UpdateUserPreferences(sl()));
  sl.registerLazySingleton(() => GetWishlistHotels(sl()));
  sl.registerLazySingleton(() => AddToWishlist(sl()));
  sl.registerLazySingleton(() => RemoveFromWishlist(sl()));
  
  //! Features - Repositories
  sl.registerLazySingleton<HotelRepository>(
    () => HotelRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );
  
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );
  
  sl.registerLazySingleton<BookingRepository>(
    () => BookingRepositoryImpl(
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );
  
  sl.registerLazySingleton<SearchRepository>(
    () => SearchRepositoryImpl(
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );
  
  sl.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(
      sharedPreferences: sl(),
      networkInfo: sl(),
    ),
  );

  //! Features - Data sources
  // Remote data sources
  sl.registerLazySingleton<HotelRemoteDataSource>(
    () => HotelRemoteDataSourceImpl(client: sl()),
  );
  
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(client: sl()),
  );

  // Local data sources
  sl.registerLazySingleton<HotelLocalDataSource>(
    () => HotelLocalDataSourceImpl(sharedPreferences: sl()),
  );
  
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sharedPreferences: sl()),
  );
  
  sl.registerLazySingleton<BookingLocalDataSource>(
    () => BookingLocalDataSourceImpl(sharedPreferences: sl()),
  );
  
  sl.registerLazySingleton<SearchLocalDataSource>(
    () => SearchLocalDataSourceImpl(sharedPreferences: sl()),
  );

  //! Core
  sl.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(sl()),
  );

  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  sl.registerLazySingleton(() => http.Client());
  sl.registerLazySingleton(() => Connectivity());
}

/// Reset all dependencies (useful for testing)
Future<void> reset() async {
  await sl.reset();
}
