import 'package:flutter/material.dart';

/// Responsive design helper utilities for consistent sizing across devices
class ResponsiveHelper {
  // Base design dimensions (iPhone 12 Pro as reference)
  static const double _baseWidth = 390.0;
  static const double _baseHeight = 844.0;
  
  /// Get responsive width based on percentage of screen width
  static double getResponsiveWidth(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.width * percentage;
  }
  
  /// Get responsive height based on percentage of screen height
  static double getResponsiveHeight(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.height * percentage;
  }
  
  /// Get responsive font size that scales with screen width
  static double getResponsiveFontSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / _baseWidth;
    return baseSize * scaleFactor.clamp(0.8, 1.3); // Limit scaling between 80% and 130%
  }
  
  /// Get responsive padding based on screen width
  static double getResponsivePadding(BuildContext context, double basePadding) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / _baseWidth;
    return basePadding * scaleFactor.clamp(0.8, 1.2);
  }
  
  /// Get responsive border radius
  static double getResponsiveBorderRadius(BuildContext context, double baseRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / _baseWidth;
    return baseRadius * scaleFactor.clamp(0.8, 1.2);
  }
  
  /// Get responsive icon size
  static double getResponsiveIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / _baseWidth;
    return baseSize * scaleFactor.clamp(0.8, 1.3);
  }
  
  /// Check if device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }
  
  /// Get safe responsive height (excluding safe areas)
  static double getSafeResponsiveHeight(BuildContext context, double percentage) {
    final mediaQuery = MediaQuery.of(context);
    final safeHeight = mediaQuery.size.height - 
                     mediaQuery.padding.top - 
                     mediaQuery.padding.bottom;
    return safeHeight * percentage;
  }
  
  /// Get responsive spacing for consistent gaps
  static double getResponsiveSpacing(BuildContext context, ResponsiveSpacing spacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = screenWidth / _baseWidth;
    
    switch (spacing) {
      case ResponsiveSpacing.xs:
        return 4.0 * scaleFactor.clamp(0.8, 1.2);
      case ResponsiveSpacing.sm:
        return 8.0 * scaleFactor.clamp(0.8, 1.2);
      case ResponsiveSpacing.md:
        return 16.0 * scaleFactor.clamp(0.8, 1.2);
      case ResponsiveSpacing.lg:
        return 24.0 * scaleFactor.clamp(0.8, 1.2);
      case ResponsiveSpacing.xl:
        return 32.0 * scaleFactor.clamp(0.8, 1.2);
      case ResponsiveSpacing.xxl:
        return 48.0 * scaleFactor.clamp(0.8, 1.2);
    }
  }
  
  /// Get responsive button height
  static double getResponsiveButtonHeight(BuildContext context, ButtonSize size) {
    final screenHeight = MediaQuery.of(context).size.height;
    final scaleFactor = screenHeight / _baseHeight;
    
    switch (size) {
      case ButtonSize.small:
        return 36.0 * scaleFactor.clamp(0.8, 1.2);
      case ButtonSize.medium:
        return 48.0 * scaleFactor.clamp(0.8, 1.2);
      case ButtonSize.large:
        return 56.0 * scaleFactor.clamp(0.8, 1.2);
    }
  }
  
  /// Get responsive card dimensions
  static Size getResponsiveCardSize(BuildContext context, {
    double widthPercentage = 0.9,
    double aspectRatio = 1.6,
  }) {
    final width = getResponsiveWidth(context, widthPercentage);
    final height = width / aspectRatio;
    return Size(width, height);
  }
}

/// Enum for responsive spacing options
enum ResponsiveSpacing { xs, sm, md, lg, xl, xxl }

/// Enum for button sizes
enum ButtonSize { small, medium, large }

/// Extension methods for easier responsive design
extension ResponsiveExtension on BuildContext {
  /// Quick access to responsive width
  double rw(double percentage) => ResponsiveHelper.getResponsiveWidth(this, percentage);
  
  /// Quick access to responsive height
  double rh(double percentage) => ResponsiveHelper.getResponsiveHeight(this, percentage);
  
  /// Quick access to responsive font size
  double rf(double baseSize) => ResponsiveHelper.getResponsiveFontSize(this, baseSize);
  
  /// Quick access to responsive padding
  double rp(double basePadding) => ResponsiveHelper.getResponsivePadding(this, basePadding);
  
  /// Quick access to responsive spacing
  double rs(ResponsiveSpacing spacing) => ResponsiveHelper.getResponsiveSpacing(this, spacing);
  
  /// Quick access to responsive border radius
  double rr(double baseRadius) => ResponsiveHelper.getResponsiveBorderRadius(this, baseRadius);
  
  /// Quick access to responsive icon size
  double ri(double baseSize) => ResponsiveHelper.getResponsiveIconSize(this, baseSize);
}
