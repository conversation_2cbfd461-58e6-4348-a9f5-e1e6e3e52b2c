/// Repository contract for splash functionality
library;

import '../entities/app_initialization.dart';

/// Abstract repository for splash screen functionality
abstract class SplashRepository {
  /// Get app initialization data
  Future<AppInitialization> getAppInitializationData();
  
  /// Check if user has completed onboarding
  Future<bool> hasCompletedOnboarding();
  
  /// Check if user is logged in
  Future<bool> isUserLoggedIn();
  
  /// Set onboarding completion status
  Future<void> setOnboardingCompleted(bool completed);
  
  /// Initialize the app with necessary data and configurations
  Future<void> initializeApp();
}
