import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/shared/widgets/custombutton_widget.dart';

class CompletionCelebrationCard extends StatefulWidget {
  final String userName;
  final VoidCallback onComplete;

  const CompletionCelebrationCard({
    super.key,
    required this.userName,
    required this.onComplete,
  });

  @override
  State<CompletionCelebrationCard> createState() => _CompletionCelebrationCardState();
}

class _CompletionCelebrationCardState extends State<CompletionCelebrationCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.4, 1.0, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenHeight = screenSize.height;
    final screenWidth = screenSize.width;
    
    // Responsive sizing based on screen dimensions
    final padding = screenWidth * 0.05; // 6% of screen width
    final iconSize = screenHeight * 0.12; // 15% of screen height
    final iconFontSize = screenHeight * 0.07; // 7% of screen height
    final titleFontSize = screenHeight * 0.030; // 3.5% of screen height
    final subtitleFontSize = screenHeight * 0.020; // 2.5% of screen height
    final bodyFontSize = screenHeight * 0.016; // 2% of screen height
    final smallFontSize = screenHeight * 0.01; // 1.8% of screen height
    final buttonHeight = screenHeight * 0.07; // 7% of screen height
    final borderRadius = screenWidth * 0.04; // 4% of screen width
    
    // Spacing based on screen height
    final spacingSmall = screenHeight * 0.02; // 2% of screen height
    final spacingMedium = screenHeight * 0.03; // 3% of screen height
    final spacingLarge = screenHeight * 0.05; // 5% of screen height
    final spacingExtraLarge = screenHeight * 0.07; // 7% of screen height

    return Padding(
      padding: EdgeInsets.all(padding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
              // Success icon with scale animation
              AnimatedBuilder(
                animation: _scaleAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Container(
                      width: iconSize,
                      height: iconSize,
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(iconSize / 2),
                      ),
                      child: Center(
                        child: Text(
                          '🎉',
                          style: TextStyle(fontSize: iconFontSize),
                        ),
                      ),
                    ),
                  );
                },
              ),

              SizedBox(height: spacingLarge),

              // Success message with fade animation
              FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    Text(
                      'Perfect! You\'re all set!',
                      style: AppTextStyles.headline1.copyWith(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.bold,
                        color: AppColors.text,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: spacingSmall),

                    Text(
                      'Welcome to KindAli, ${widget.userName}! 👋',
                      style: AppTextStyles.headline2.copyWith(
                        fontSize: subtitleFontSize,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: spacingMedium),

                    Text(
                      'Your profile is complete and you\'re ready to start exploring amazing hotels and experiences.',
                      style: AppTextStyles.bodyText1.copyWith(
                        fontSize: bodyFontSize,
                        color: AppColors.textLight,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              SizedBox(height: spacingLarge),

              // Features unlocked with slide animation
              SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    padding: EdgeInsets.all(padding),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(borderRadius),
                      border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.1),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'What\'s next?',
                          style: AppTextStyles.subtitle1.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.text,
                            fontSize: subtitleFontSize,
                          ),
                        ),
                        SizedBox(height: spacingSmall),
                        _buildFeatureItem(
                          icon: Icons.search,
                          title: 'Discover Hotels',
                          description: 'Browse thousands of amazing properties',
                          iconSize: screenHeight * 0.04, // 4% of screen height
                          titleFontSize: bodyFontSize,
                          descriptionFontSize: smallFontSize,
                        ),
                        SizedBox(height: spacingSmall),
                        _buildFeatureItem(
                          icon: Icons.bookmark,
                          title: 'Save Favorites',
                          description: 'Keep track of places you love',
                          iconSize: screenHeight * 0.04,
                          titleFontSize: bodyFontSize,
                          descriptionFontSize: smallFontSize,
                        ),
                        SizedBox(height: spacingSmall),
                        _buildFeatureItem(
                          icon: Icons.card_giftcard,
                          title: 'Earn Rewards',
                          description: 'Get points with every booking',
                          iconSize: screenHeight * 0.04,
                          titleFontSize: bodyFontSize,
                          descriptionFontSize: smallFontSize,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              SizedBox(height: spacingExtraLarge),

              // Start exploring button
              SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: CustombuttonWidget(
                    text: 'Start Exploring! 🚀',
                    backgroundColor: AppColors.primary,
                    textColor: Colors.white,
                    borderRadius: borderRadius,
                    height: buttonHeight,
                    isFullWidth: true,
                    onPressed: widget.onComplete,
                    textStyle: AppTextStyles.button.copyWith(
                      fontSize: bodyFontSize,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              SizedBox(height: spacingMedium),

              // Thank you message
              FadeTransition(
                opacity: _fadeAnimation,
                child: Text(
                  'Thank you for joining KindAli! 💙',
                  style: AppTextStyles.bodyText2.copyWith(
                    color: AppColors.textLight,
                    fontSize: smallFontSize,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        );

  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required double iconSize,
    required double titleFontSize,
    required double descriptionFontSize,
  }) {
    return Row(
      children: [
        Container(
          width: iconSize,
          height: iconSize,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(iconSize * 0.25),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: iconSize * 0.5,
          ),
        ),
        SizedBox(width: iconSize * 0.375),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyText1.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.text,
                  fontSize: titleFontSize,
                ),
              ),
              Text(
                description,
                style: AppTextStyles.bodyText2.copyWith(
                  color: AppColors.textLight,
                  fontSize: descriptionFontSize,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
