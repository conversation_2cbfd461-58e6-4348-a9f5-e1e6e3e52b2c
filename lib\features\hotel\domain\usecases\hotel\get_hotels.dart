﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/hotel_entity.dart';
import '../../repositories/hotel_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for getting hotels with optional filtering
class GetHotels implements UseCase<List<HotelEntity>, GetHotelsParams> {
  final HotelRepository repository;

  GetHotels(this.repository);

  @override
  Future<Either<Failure, List<HotelEntity>>> call(GetHotelsParams params) async {
    return await repository.getHotels();
  }
}

/// Parameters for GetHotels use case
class GetHotelsParams extends Equatable {
  final int? cityId;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int? guests;
  final int? rooms;

  const GetHotelsParams({
    this.cityId,
    this.checkInDate,
    this.checkOutDate,
    this.guests,
    this.rooms,
  });

  @override
  List<Object?> get props => [
        cityId,
        checkInDate,
        checkOutDate,
        guests,
        rooms,
      ];
}

