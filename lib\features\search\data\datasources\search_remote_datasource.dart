import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../core/constants/api_constants.dart';
import '../../../../core/error/exceptions.dart';
import '../models/search_cities.dart';

abstract class SearchRemoteDataSource {
  Future<List<SearchCities>> searchCities(String query);
}

class SearchRemoteDataSourceImpl implements SearchRemoteDataSource {
  final http.Client client;

  SearchRemoteDataSourceImpl({required this.client});

  @override
  Future<List<SearchCities>> searchCities(String query) async {
    try {
      final response = await client.get(
        Uri.parse('${ApiConstants.fullBaseUrl}${ApiConstants.autoSuggest}?q=$query'),
        headers: {
          ApiConstants.contentType: ApiConstants.applicationJson,
        },
      ).timeout(const Duration(seconds: ApiConstants.connectionTimeout));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData.map((e) => SearchCities.fromJson(e)).toList();
      } else {
        throw ServerException('Failed to search cities: ${response.statusCode}');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }
}