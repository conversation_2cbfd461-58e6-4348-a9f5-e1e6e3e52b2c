/// Use case for initializing app data and configurations
library;

import '../entities/app_initialization.dart';
import '../repositories/splash_repository.dart';

/// Use case to initialize app data and get initialization information
class InitializeAppData {
  final SplashRepository _repository;

  InitializeAppData(this._repository);

  /// Execute the use case
  /// Returns app initialization data with navigation route
  Future<AppInitialization> call() async {
    try {
      // Initialize the app
      await _repository.initializeApp();
      
      // Get initialization data
      final initData = await _repository.getAppInitializationData();
      
      return initData;
    } catch (e) {
      // On error, return default initialization data
      return AppInitialization(
        hasCompletedOnboarding: false,
        isUserLoggedIn: false,
        appVersion: '1.0.0',
        lastLaunchTime: DateTime.now(),
        navigationRoute: '/dashboard',
      );
    }
  }
}
