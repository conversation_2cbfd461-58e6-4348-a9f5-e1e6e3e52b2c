import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';

class BuildofferStripSection extends StatelessWidget {
  final String title;
  final String subtitle;
  final String description;
  final String promoCode;
  final String image;

  
  const BuildofferStripSection({
    super.key, 
    required this.title, 
    required this.subtitle, 
    required this.description,
    required this.promoCode,
    required this.image,
   
  });

  void _handleOfferClick() {
    print("Clicked offer: $title");
    // Implement navigation or action logic
  }

  void _handleViewDetails() {
    print("View details for: $promoCode");
    // Implement view details logic
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _handleOfferClick,
      child: Container(
        width: double.infinity,
        height: 280,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(30),topRight: Radius.circular(10),bottomRight:Radius.circular(10) ),
          border: Border.all(color: Colors.grey.withOpacity(0.2)),
          // boxShadow: [
          //   BoxShadow(
          //     color: Colors.black.withOpacity(0.1),
          //     blurRadius: 16,
          //     offset: const Offset(0, 4),
          //   ),
          // ],
        ),
        child: Row(
          children: [
            // Image section with curved overlay
            SizedBox(
              width: 140,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(30),
                      bottomLeft: Radius.circular(0),
                    ),
                    child: Image.network(
                      image,
                      width: 160,
                      height: 140,
                      fit: BoxFit.cover,
                 
                    ),
                  ),
                  // Curved overlay effect
                  Positioned(
                    right: -20,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: 60,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          bottomLeft: Radius.circular(0),)
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Content section
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Title and description
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 19,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1A1A1A),
                              height: 1.25,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 5),
                          Text(
                            subtitle,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Color(0xFF555555),
                              fontWeight: FontWeight.w500,
                              height: 1.3,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 7),
                          Text(
                            description,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Color(0xFF777777),
                              height: 1.4,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    
                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                       
                        GestureDetector(
                          onTap: _handleViewDetails,
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'View Details',
                                style: TextStyle(
                                  color: Color(0xFF007BFF),
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(width: 4),
                              Icon(
                                Icons.chevron_right,
                                color: Color(0xFF007BFF),
                                size: 16,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}


