﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/booking_entity.dart';
import '../../repositories/booking_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for getting upcoming bookings
class GetUpcomingBookings implements UseCaseNoParams<List<BookingEntity>> {
  final BookingRepository repository;

  GetUpcomingBookings(this.repository);

  @override
  Future<Either<Failure, List<BookingEntity>>> call() async {
    return await repository.getUpcomingBookings();
  }
}

