import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class FacilitiesTab extends StatefulWidget {
  const FacilitiesTab({Key? key}) : super(key: key);

  @override
  State<FacilitiesTab> createState() => _FacilitiesTabState();
}

class _FacilitiesTabState extends State<FacilitiesTab> {
  // Currently selected facility section
  int _selectedIndex = 0;

  // Facility data
  List<FacilitySection> get _facilitySections => [
    FacilitySection(
      title: 'hotel.facilities.roomAmenities'.tr,
      icon: Icons.hotel,
      items: [
        'hotel.facilities.airConditioned'.tr,
        'hotel.facilities.dailyHousekeeping'.tr,
        'hotel.facilities.freeDrinkingWater'.tr,
        'hotel.facilities.hotColdWater'.tr,
        'hotel.facilities.teaCoffeeMaker'.tr,
        'hotel.facilities.safetyDepositBox'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.internetAccess'.tr,
      icon: Icons.wifi,
      items: [
        'hotel.facilities.internetServices'.tr,
        'hotel.facilities.freeWifi'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.foodAndDrinks'.tr,
      icon: Icons.restaurant,
      items: [
        'hotel.facilities.continentalBreakfast'.tr,
        'hotel.facilities.roomService24'.tr,
        'hotel.facilities.bar'.tr,
        'hotel.facilities.coffeeShop'.tr,
        'hotel.facilities.inRoomBreakfast'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.activitiesAndSports'.tr,
      icon: Icons.sports_tennis,
      items: [
        'hotel.facilities.indoorSwimmingPool'.tr,
        'hotel.facilities.fitnessCenter'.tr,
        'hotel.facilities.spaSauna'.tr,
        'hotel.facilities.bicycleRental'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.servicesAndConveniences'.tr,
      icon: Icons.luggage,
      items: [
        'hotel.facilities.disabledFacilities'.tr,
        'hotel.facilities.luggageStorage'.tr,
        'hotel.facilities.laundryService'.tr,
        'hotel.facilities.chapel'.tr,
        'hotel.facilities.doorman'.tr,
        'hotel.facilities.dryCleaning'.tr,
        'hotel.facilities.library'.tr,
        'hotel.facilities.postalService'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.safetyAndCleanliness'.tr,
      icon: Icons.cleaning_services,
      items: [
        'hotel.facilities.cashlessPayment'.tr,
        'hotel.facilities.commonAreaDisinfection'.tr,
        'hotel.facilities.doctorOnCall'.tr,
        'hotel.facilities.freeFaceMasks'.tr,
        'hotel.facilities.noSharedStationery'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.kitchen'.tr,
      icon: Icons.coffee,
      items: [
        'hotel.facilities.teaCoffeeMaker'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.bathroom'.tr,
      icon: Icons.shower,
      items: [
        'hotel.facilities.shower'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.accessibility'.tr,
      icon: Icons.elevator,
      items: [
        'hotel.facilities.elevatorAccess'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.transfersAndTransport'.tr,
      icon: Icons.airport_shuttle,
      items: [
        'hotel.facilities.onsiteParking'.tr,
        'hotel.facilities.airportTransfer'.tr,
        'hotel.facilities.carRentalService'.tr,
        'hotel.facilities.shuttleService'.tr,
        'hotel.facilities.taxiCabService'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.access'.tr,
      icon: Icons.desktop_windows,
      items: [
        'hotel.facilities.frontDesk24'.tr,
        'hotel.facilities.nonSmokingRooms'.tr,
        'hotel.facilities.privateCheckInOut'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.safetyAndSecurity'.tr,
      icon: Icons.videocam,
      items: [
        'hotel.facilities.security24'.tr,
        'hotel.facilities.cctvCommonAreas'.tr,
        'hotel.facilities.fireExtinguisher'.tr,
      ],
    ),
    FacilitySection(
      title: 'hotel.facilities.languagesSpoken'.tr,
      icon: Icons.language,
      items: [
        'hotel.facilities.english'.tr,
        'hotel.facilities.hindi'.tr,
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height *
          0.8, // Set a specific height constraint
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // _buildSectionTitle('Facilities'),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left sidebar with facility icons
                _buildFacilityIconsSidebar(),
                // Right content area showing selected facility details
                Expanded(
                  child: _buildFacilityDetailsContainer(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

Widget _buildFacilityIconsSidebar() {
  return Container(
    width: 80,
    padding: const EdgeInsets.symmetric(vertical: 16),
    decoration: BoxDecoration(
      color: Colors.grey[100],
      borderRadius: const BorderRadius.only(
        topRight: Radius.circular(20),
        bottomRight: Radius.circular(20),
      ),
    ),
    child: Stack(
      children: [
        // Scrollable icons
        SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(_facilitySections.length, (index) {
              return _buildFacilityIconItem(index);
            }),
          ),
        ),
        // Arrow at bottom
        Align(
          alignment: Alignment.bottomRight,
          child: Container(
            padding: const EdgeInsets.only(bottom: 8),
            child: Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.background,
              size: 40,
            ),
          ),
        ),
      ],
    ),
  );
}


Widget _buildFacilityIconItem(int index) {
  final bool isSelected = index == _selectedIndex;
  final FacilitySection section = _facilitySections[index];

  return InkWell(
    onTap: () {
      setState(() {
        _selectedIndex = index;
      });
    },
    child: Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary : Colors.transparent,
        borderRadius: isSelected
            ? const BorderRadius.only(
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
              )
            : BorderRadius.zero,
      ),
      child: Column(
        children: [
          Icon(
            section.icon,
            color: isSelected ? Colors.white : AppColors.secondary,
            size: 28,
          ),
          const SizedBox(height: 6),
          Text(
            section.title.split(' ')[0],
            style: TextStyle(
              color: isSelected ? Colors.white : AppColors.textLight,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}


  Widget _buildFacilityDetailsContainer() {
    final FacilitySection selectedSection = _facilitySections[_selectedIndex];

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                selectedSection.icon,
                color: AppColors.primary,
                size: 30,
              ),
              const SizedBox(width: 16),
              Text(
                selectedSection.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(height: 32),
          Expanded(
            child: ListView.builder(
              itemCount: selectedSection.items.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: 18,
                        color: AppColors.secondary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          selectedSection.items[index],
                          style: const TextStyle(fontSize: 15),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Model class to hold facility data
class FacilitySection {
  final String title;
  final IconData icon;
  final List<String> items;

  FacilitySection({
    required this.title,
    required this.icon,
    required this.items,
  });
}
