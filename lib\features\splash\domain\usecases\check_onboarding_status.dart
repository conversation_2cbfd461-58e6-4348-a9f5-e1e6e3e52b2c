/// Use case for checking onboarding completion status
library;

import '../repositories/splash_repository.dart';

/// Use case to check if user has completed onboarding
class CheckOnboardingStatus {
  final SplashRepository _repository;

  CheckOnboardingStatus(this._repository);

  /// Execute the use case
  /// Returns true if onboarding is completed, false otherwise
  Future<bool> call() async {
    try {
      return await _repository.hasCompletedOnboarding();
    } catch (e) {
      // On error, assume onboarding is not completed
      return false;
    }
  }
}
