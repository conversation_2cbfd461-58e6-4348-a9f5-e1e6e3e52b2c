/// Central export file for all Riverpod StateNotifier providers
/// This file provides a single import point for all presentation layer providers
library;

// Export all StateNotifier providers from features
export '../../features/authentication/presentation/providers/auth_notifier.dart';
export '../../features/booking/presentation/providers/booking_notifier.dart';
export '../../features/booking/presentation/providers/room_selection_notifier.dart';
export '../../features/home/<USER>/providers/home_notifier.dart';
export '../../features/hotel/presentation/providers/hotel_details_notifier.dart';
export '../../features/hotel/presentation/providers/hotel_list_notifier.dart';
export '../../features/profile/presentation/providers/profile_notifier.dart';
export '../../features/search/presentation/providers/search_cities_notifier.dart' hide searchCitiesProvider;
export '../../features/wishlist/presentation/providers/wishlist_notifier.dart';

// Export shared providers
export '../../shared/presentation/providers/currency_notifier.dart';
export '../../shared/presentation/providers/localization_notifier.dart';

// Export core Riverpod providers for dependency injection
export '../../features/authentication/presentation/providers/auth_providers.dart';
export '../../features/booking/presentation/providers/booking_providers.dart';
export '../../features/hotel/presentation/providers/hotel_providers.dart';
export '../../features/search/presentation/providers/search_providers.dart';
