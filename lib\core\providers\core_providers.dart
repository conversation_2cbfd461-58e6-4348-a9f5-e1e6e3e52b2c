import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../network/network_info.dart';

part 'core_providers.g.dart';

/// Shared Preferences provider
@riverpod
Future<SharedPreferences> sharedPreferences(Ref ref) async {
  return await SharedPreferences.getInstance();
}

/// HTTP Client provider
@riverpod
http.Client httpClient(Ref ref) {
  return http.Client();
}

/// Connectivity provider
@riverpod
Connectivity connectivity(Ref ref) {
  return Connectivity();
}

/// Network Info provider
@riverpod
NetworkInfo networkInfo(Ref ref) {
  return NetworkInfoImpl(ref.watch(connectivityProvider));
}
