/// Hotel list state management using Riverpod StateNotifier
library;

import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../hotel/data/models/hotel_details.dart';

/// Hotel list state class
class HotelListState {
  final String destination;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int guests;
  final bool isLoading;
  final String? error;
  final bool isFilterOpen;
  final String sortOption;
  final List<InventoryInfoList> hotels;
  final List<InventoryInfoList> filteredHotels;

  const HotelListState({
    this.destination = '',
    this.checkInDate,
    this.checkOutDate,
    this.guests = 1,
    this.isLoading = false,
    this.error,
    this.isFilterOpen = false,
    this.sortOption = 'popularity',
    this.hotels = const [],
    this.filteredHotels = const [],
  });

  /// Check if has hotels
  bool get hasHotels => filteredHotels.isNotEmpty;

  HotelListState copyWith({
    String? destination,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? guests,
    bool? isLoading,
    String? error,
    bool? isFilterOpen,
    String? sortOption,
    List<InventoryInfoList>? hotels,
    List<InventoryInfoList>? filteredHotels,
  }) {
    return HotelListState(
      destination: destination ?? this.destination,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      guests: guests ?? this.guests,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isFilterOpen: isFilterOpen ?? this.isFilterOpen,
      sortOption: sortOption ?? this.sortOption,
      hotels: hotels ?? this.hotels,
      filteredHotels: filteredHotels ?? this.filteredHotels,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HotelListState &&
        other.destination == destination &&
        other.checkInDate == checkInDate &&
        other.checkOutDate == checkOutDate &&
        other.guests == guests &&
        other.isLoading == isLoading &&
        other.error == error &&
        other.isFilterOpen == isFilterOpen &&
        other.sortOption == sortOption &&
        other.hotels == hotels &&
        other.filteredHotels == filteredHotels;
  }

  @override
  int get hashCode => Object.hash(
        destination,
        checkInDate,
        checkOutDate,
        guests,
        isLoading,
        error,
        isFilterOpen,
        sortOption,
        hotels,
        filteredHotels,
      );
}

/// Hotel list StateNotifier
class HotelListNotifier extends StateNotifier<HotelListState> {
  HotelListNotifier() : super(const HotelListState()) {
    _initializeDefaults();
    loadHotels();
  }

  /// Initialize default values
  void _initializeDefaults() {
    final now = DateTime.now();
    state = state.copyWith(
      checkInDate: now,
      checkOutDate: now.add(const Duration(days: 3)),
      guests: 1,
    );
  }

  /// Set loading state
  void _setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// Load hotels data
  Future<void> loadHotels() async {
    _setLoading(true);
    
    try {
      final String response = await rootBundle.loadString('assets/json/hotels.json');
      final Map<String, dynamic> jsonData = json.decode(response);

      // Navigate to inventoryInfoList
      final List<dynamic>? hotelsData = jsonData['data']?['result']?['inventoryInfoList'];

      if (hotelsData != null) {
        final hotels = hotelsData.map((json) => InventoryInfoList.fromJson(json)).toList();
        state = state.copyWith(
          hotels: hotels,
          error: null,
        );
        _applyFilters();
      } else {
        throw Exception('No hotel data found in JSON');
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load hotels. Please try again.',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Apply filters and sorting
  void _applyFilters() {
    List<InventoryInfoList> filtered = List.from(state.hotels);

    // Apply sorting
    switch (state.sortOption) {
      case 'price_low_to_high':
        filtered.sort((a, b) {
          final priceA = a.fareDetail?.displayedBaseFare ?? a.fareDetail?.totalPrice ?? 0.0;
          final priceB = b.fareDetail?.displayedBaseFare ?? b.fareDetail?.totalPrice ?? 0.0;
          return priceA.compareTo(priceB);
        });
        break;
      case 'price_high_to_low':
        filtered.sort((a, b) {
          final priceA = a.fareDetail?.displayedBaseFare ?? a.fareDetail?.totalPrice ?? 0.0;
          final priceB = b.fareDetail?.displayedBaseFare ?? b.fareDetail?.totalPrice ?? 0.0;
          return priceB.compareTo(priceA);
        });
        break;
      case 'rating':
        filtered.sort((a, b) {
          final ratingA = double.tryParse(a.userRating?.toString() ?? '0') ?? a.starRating?.toDouble() ?? 0.0;
          final ratingB = double.tryParse(b.userRating?.toString() ?? '0') ?? b.starRating?.toDouble() ?? 0.0;
          return ratingB.compareTo(ratingA);
        });
        break;
      case 'popularity':
      default:
        // Keep original order for popularity
        break;
    }

    state = state.copyWith(filteredHotels: filtered);
  }

  /// Set destination
  void setDestination(String destination) {
    if (state.destination == destination) return;
    state = state.copyWith(destination: destination);
  }

  /// Set check-in date
  void setCheckInDate(DateTime? date) {
    if (state.checkInDate == date) return;
    
    var newCheckOutDate = state.checkOutDate;
    
    // If check-out date is before check-in date, update it
    if (newCheckOutDate != null && date != null && newCheckOutDate.isBefore(date)) {
      newCheckOutDate = date.add(const Duration(days: 1));
    }

    state = state.copyWith(
      checkInDate: date,
      checkOutDate: newCheckOutDate,
    );
  }

  /// Set check-out date
  void setCheckOutDate(DateTime? date) {
    if (state.checkOutDate == date) return;
    
    var newCheckInDate = state.checkInDate;
    
    // If check-in date is after check-out date, update it
    if (newCheckInDate != null && date != null && newCheckInDate.isAfter(date)) {
      newCheckInDate = date.subtract(const Duration(days: 1));
    }

    state = state.copyWith(
      checkInDate: newCheckInDate,
      checkOutDate: date,
    );
  }

  /// Set guests count
  void setGuests(int guests) {
    if (state.guests == guests) return;
    state = state.copyWith(guests: guests);
  }

  /// Toggle filter panel
  void toggleFilter() {
    state = state.copyWith(isFilterOpen: !state.isFilterOpen);
  }

  /// Set sort option
  void setSortOption(String option) {
    if (state.sortOption == option) return;
    state = state.copyWith(sortOption: option);
    _applyFilters();
  }

  /// Refresh hotels
  Future<void> refreshHotels() async {
    await loadHotels();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Initialize with search parameters
  void initializeWithSearchParams({
    String? destination,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? guests,
  }) {
    state = state.copyWith(
      destination: destination ?? state.destination,
      checkInDate: checkInDate ?? state.checkInDate,
      checkOutDate: checkOutDate ?? state.checkOutDate,
      guests: guests ?? state.guests,
    );
  }
}

/// Hotel list provider
final hotelListProvider = StateNotifierProvider<HotelListNotifier, HotelListState>(
  (ref) => HotelListNotifier(),
);
