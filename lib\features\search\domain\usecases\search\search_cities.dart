﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/features/search/domain/entities/search_entity.dart';
import '../../repositories/search_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for searching cities
class SearchCities implements UseCase<List<CityEntity>, SearchCitiesParams> {
  final SearchRepository repository;

  SearchCities(this.repository);

  @override
  Future<Either<Failure, List<CityEntity>>> call(SearchCitiesParams params) async {
    return await repository.searchCities(params.query);
  }
}

/// Parameters for SearchCities use case
class SearchCitiesParams extends Equatable {
  final String query;

  const SearchCitiesParams({required this.query});

  @override
  List<Object> get props => [query];
}

