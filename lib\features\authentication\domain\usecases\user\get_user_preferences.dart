﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/user_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for getting user preferences
class GetUserPreferences implements UseCaseNoParams<UserPreferencesEntity> {
  final UserRepository repository;

  GetUserPreferences(this.repository);

  @override
  Future<Either<Failure, UserPreferencesEntity>> call() async {
    return await repository.getUserPreferences();
  }
}

