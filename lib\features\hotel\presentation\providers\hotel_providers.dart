﻿/// Hotel providers for Riverpod dependency injection
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/providers/core_providers.dart';
import '../../data/datasources/hotel_local_datasource.dart';
import '../../data/datasources/hotel_remote_datasource.dart';
import '../../data/repositories/hotel_repository_impl.dart';
import '../../domain/repositories/hotel_repository.dart';
import '../../domain/usecases/hotel/get_featured_hotels.dart';
import '../../domain/usecases/hotel/get_hotel_details.dart';
import '../../domain/usecases/hotel/get_hotels.dart';
import '../../domain/usecases/hotel/search_hotels.dart';

part 'hotel_providers.g.dart';

// Data Sources
@riverpod
Future<HotelLocalDataSource> hotelLocalDataSource(Ref ref) async {
  final sharedPrefs = await ref.watch(sharedPreferencesProvider.future);
  return HotelLocalDataSourceImpl(sharedPreferences: sharedPrefs);
}

@riverpod
HotelRemoteDataSource hotelRemoteDataSource(Ref ref) {
  final client = ref.watch(httpClientProvider);
  return HotelRemoteDataSourceImpl(client: client);
}

// Repository
@riverpod
Future<HotelRepository> hotelRepository(Ref ref) async {
  final localDataSource = await ref.watch(hotelLocalDataSourceProvider.future);
  final remoteDataSource = ref.watch(hotelRemoteDataSourceProvider);
  final networkInfo = ref.watch(networkInfoProvider);

  return HotelRepositoryImpl(
    localDataSource: localDataSource,
    remoteDataSource: remoteDataSource,
    networkInfo: networkInfo,
  );
}

// Use Cases
@riverpod
Future<GetHotels> getHotels(Ref ref) async {
  final repository = await ref.watch(hotelRepositoryProvider.future);
  return GetHotels(repository);
}

@riverpod
Future<GetFeaturedHotels> getFeaturedHotels(Ref ref) async {
  final repository = await ref.watch(hotelRepositoryProvider.future);
  return GetFeaturedHotels(repository);
}

@riverpod
Future<GetHotelDetails> getHotelDetails(Ref ref) async {
  final repository = await ref.watch(hotelRepositoryProvider.future);
  return GetHotelDetails(repository);
}

@riverpod
Future<SearchHotels> searchHotels(Ref ref) async {
  final repository = await ref.watch(hotelRepositoryProvider.future);
  return SearchHotels(repository);
}



