import 'package:flutter/material.dart';
import 'package:kind_ali/core/services/location_service.dart';
import 'package:kind_ali/shared/widgets/location_permission_dialog.dart';

/// Helper class for location-related operations
class LocationHelper {
  /// Handle location result and show appropriate UI
  static Future<void> handleLocationResult(
    BuildContext context,
    LocationResult result, {
    VoidCallback? onSuccess,
    VoidCallback? onRetry,
  }) async {
    if (result.isSuccess) {
      onSuccess?.call();
    } else {
      await _showLocationError(context, result, onRetry: onRetry);
    }
  }

  /// Show location error dialog based on error type
  static Future<void> _showLocationError(
    BuildContext context,
    LocationResult result, {
    VoidCallback? onRetry,
  }) async {
    if (result.errorType == null) return;

    await LocationPermissionDialog.show(
      context,
      errorType: result.errorType!,
      message: result.error ?? 'Unknown location error',
      onRetry: onRetry,
    );
  }

  /// Get location with user-friendly error handling
  static Future<LocationResult> getLocationWithErrorHandling(
    BuildContext context, {
    bool useCache = true,
    bool showErrorDialog = true,
    VoidCallback? onRetry,
  }) async {
    final result = await LocationService.getCurrentLocation(useCache: useCache);

    if (!result.isSuccess && showErrorDialog && context.mounted) {
      await handleLocationResult(context, result, onRetry: onRetry);
    }

    return result;
  }

  /// Check if location services are available
  static Future<bool> isLocationAvailable() async {
    return await LocationService.hasLocationPermission();
  }

  /// Get cached location or null
  static Future<LocationResult?> getCachedLocation() async {
    final result = await LocationService.getCurrentLocation(useCache: true);
    return result.isFromCache ? result : null;
  }

  /// Format location name for display
  static String formatLocationName(String locationName, {bool isFromCache = false}) {
    if (isFromCache) {
      return '$locationName (cached)';
    }
    return locationName;
  }

  /// Show location loading snackbar
  static ScaffoldFeatureController<SnackBar, SnackBarClosedReason> showLocationLoading(
    BuildContext context, {
    String message = 'Getting your location...',
  }) {
    return ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            Text(message),
          ],
        ),
        duration: const Duration(seconds: 10),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show location success snackbar
  static ScaffoldFeatureController<SnackBar, SnackBarClosedReason> showLocationSuccess(
    BuildContext context,
    String locationName, {
    bool isFromCache = false,
  }) {
    return ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isFromCache ? Icons.cached : Icons.location_on,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                isFromCache 
                    ? 'Using cached location: $locationName'
                    : 'Location found: $locationName',
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show location error snackbar
  static ScaffoldFeatureController<SnackBar, SnackBarClosedReason> showLocationError(
    BuildContext context,
    String error,
  ) {
    return ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(error)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Settings',
          textColor: Colors.white,
          onPressed: () => LocationService.openAppSettings(),
        ),
      ),
    );
  }

  /// Calculate and format distance
  static String formatDistance(double distanceKm) {
    if (distanceKm < 1) {
      return '${(distanceKm * 1000).round()}m away';
    } else if (distanceKm < 10) {
      return '${distanceKm.toStringAsFixed(1)}km away';
    } else {
      return '${distanceKm.round()}km away';
    }
  }

  /// Get distance between two locations
  static double getDistance(
    double lat1, double lon1,
    double lat2, double lon2,
  ) {
    return LocationService.calculateDistance(lat1, lon1, lat2, lon2);
  }

  /// Check if location is within radius
  static bool isWithinRadius(
    double lat1, double lon1,
    double lat2, double lon2,
    double radiusKm,
  ) {
    final distance = getDistance(lat1, lon1, lat2, lon2);
    return distance <= radiusKm;
  }

  /// Sort locations by distance from user location
  static List<T> sortByDistance<T>(
    List<T> items,
    double userLat,
    double userLon,
    double Function(T) getItemLat,
    double Function(T) getItemLon,
  ) {
    final itemsWithDistance = items.map((item) {
      final distance = getDistance(
        userLat, userLon,
        getItemLat(item), getItemLon(item),
      );
      return MapEntry(item, distance);
    }).toList();

    itemsWithDistance.sort((a, b) => a.value.compareTo(b.value));
    return itemsWithDistance.map((entry) => entry.key).toList();
  }

  /// Filter locations within radius
  static List<T> filterByRadius<T>(
    List<T> items,
    double userLat,
    double userLon,
    double radiusKm,
    double Function(T) getItemLat,
    double Function(T) getItemLon,
  ) {
    return items.where((item) {
      return isWithinRadius(
        userLat, userLon,
        getItemLat(item), getItemLon(item),
        radiusKm,
      );
    }).toList();
  }
}
