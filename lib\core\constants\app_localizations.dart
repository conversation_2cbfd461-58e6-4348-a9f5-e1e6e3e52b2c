import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppLocalizations {
  final Locale locale;
  late Map<String, dynamic> _localizedStrings;

  AppLocalizations(this.locale);

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  Future<bool> load() async {
    try {
      final jsonString = await rootBundle.loadString('assets/language/${locale.languageCode}.json');
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      _localizedStrings = jsonMap;
      return true;
    } catch (e) {
      debugPrint('Error loading localization file: $e');
      _localizedStrings = {};
      return false;
    }
  }

  // Support nested keys like 'settings.language' or 'common.close'
  String translate(String key) {
    try {
      final keys = key.split('.');
      dynamic current = _localizedStrings;
      
      for (String k in keys) {
        if (current is Map<String, dynamic> && current.containsKey(k)) {
          current = current[k];
        } else {
          return '** $key **';
        }
      }
      
      return current?.toString() ?? '** $key **';
    } catch (e) {
      debugPrint('Error translating key: $key, Error: $e');
      return '** $key **';
    }
  }

  // Helper method for common translations
  String get close => translate('common.close');
  String get save => translate('common.save');
  String get cancel => translate('common.cancel');
  String get loading => translate('common.loading');
  String get error => translate('common.error');
  String get retry => translate('common.retry');
  String get search => translate('common.search');
  String get apply => translate('common.apply');
  String get back => translate('common.back');
  String get next => translate('common.next');
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => 
      ['en', 'ar', 'es', 'fr', 'hi'].contains(locale.languageCode);

  @override
  Future<AppLocalizations> load(Locale locale) async {
    final localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_) => false;
}