import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/shared/widgets/safe_consumer_widget.dart';
import 'package:kind_ali/core/utils/extensions.dart';
import 'package:kind_ali/core/utils/responsive_helper.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>';
import 'package:kind_ali/features/profile/presentation/pages/language/widget/language_bottomsheet.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/profile_screen.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/widgets/currency_bottomsheet.dart';
import 'package:kind_ali/features/authentication/presentation/pages/authentication/login_bottom_sheet.dart';

class DashboardScreen extends SafeConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  SafeConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends SafeConsumerState<DashboardScreen> {

  // Helper method for safe translations
  String _tr(String key) => safeTr(key);

  List<Map<String, dynamic>> _getServices() => [
    {
      'id': 'hotels',
      'svgPath': AppImages.hotel, // Hotel SVG for hotels service
      'title': _tr('dashboard.hotels'),
      'subtitle': '1000+ properties',
      'route': '/'
    },
    {
      'id': 'flights',
      'svgPath': AppImages.flight, // Plane SVG for flights service
      'title': _tr('dashboard.flights'),
      'subtitle': 'Best deals worldwide',
      'route': '/flights'
    },
    {
      'id': 'carrental',
      'svgPath': AppImages.ship, // Ship SVG for car rental service
      'title': _tr('dashboard.ship'),
      'subtitle': 'Premium vehicles',
      'route': '/car-rental'
    },
    {
      'id': 'airporttaxi',
      'svgPath': AppImages.visa, // Visa SVG for airport taxi service
      'title': _tr('dashboard.visa'),
      'subtitle': '24/7 service',
      'route': '/airport-taxi'
    },
  ];
  final List<Map<String, dynamic>> _offerBanners = [
    {
      'title': 'MEGA SALE',
      'subtitle': 'Up to 50% OFF',
      'description': 'On all hotel bookings',
      'code': 'HOTEL50',
      'color': Colors.orange,
      'icon': Icons.local_offer,
    },
    {
      'title': 'FLIGHT DEALS',
      'subtitle': '₹2000 OFF',
      'description': 'On domestic flights',
      'code': 'FLY2000',
      'color': Colors.blue,
      'icon': Icons.flight_takeoff,
    },
    {
      'title': 'WEEKEND SPECIAL',
      'subtitle': '30% OFF',
      'description': 'Car rentals & taxi',
      'code': 'WEEKEND30',
      'color': Colors.green,
      'icon': Icons.directions_car,
    },
  ];

  final List<Map<String, dynamic>> _popularDestinations = [
    {
      'name': 'Goa',
      'image': 'https://images.unsplash.com/photo-1512343879784-a960bf40e7f2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      'rating': 4.8,
      'price': '₹3,999',
      'description': 'Beach paradise',
    },
    {
      'name': 'Kerala',
      'image': 'https://images.unsplash.com/photo-1602216056096-3b40cc0c9944?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      'rating': 4.9,
      'price': '₹4,500',
      'description': 'God\'s own country',
    },
    {
      'name': 'Rajasthan',
      'image': 'https://images.unsplash.com/photo-1599661046289-e31897846e41?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      'rating': 4.7,
      'price': '₹5,200',
      'description': 'Royal heritage',
    },
    {
      'name': 'Himachal',
      'image': 'https://images.unsplash.com/photo-1626621341517-bbf3d9990a23?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      'rating': 4.6,
      'price': '₹3,800',
      'description': 'Mountain escape',
    },
  ];


  int _currentIndex = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Wait for authentication state to load before checking login status
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _checkAuthenticationState();
      }
    });
  }

  // Check authentication state and show login bottom sheet if needed
  void _checkAuthenticationState() {
    final authState = ref.read(authProvider);

    // If still initializing, wait and check again
    if (authState.isInitializing) {
      // Schedule a check for the next frame instead of using ref.listen outside build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          final currentState = ref.read(authProvider);
          if (!currentState.isInitializing) {
            _showLoginBottomSheetIfNeeded(currentState);
          } else {
            // If still initializing, check again after a short delay
            Future.delayed(const Duration(milliseconds: 100), () {
              if (mounted) {
                _checkAuthenticationState();
              }
            });
          }
        }
      });
    } else {
      // Already initialized, check login status immediately
      _showLoginBottomSheetIfNeeded(authState);
    }
  }

  // Show login bottom sheet if user is not logged in
  void _showLoginBottomSheetIfNeeded(AuthState authState) {
    if (!authState.isLoggedIn && mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        isDismissible: true,
        enableDrag: true,
        builder: (context) => const LoginBottomSheet(),
      );
    }
  }

  void _navigateToService(String serviceId, String route) {
    switch (serviceId.toLowerCase()) {
      case 'hotels':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HomeScreen(selectedServiceIndex: 0),
          ),
        );
        break;
      case 'flights':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HomeScreen(selectedServiceIndex: 1),
          ),
        );
        break;
      case 'carrental':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HomeScreen(selectedServiceIndex: 2),
          ),
        );
        break;
      case 'airporttaxi':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HomeScreen(selectedServiceIndex: 3),
          ),
        );
        break;
      default:
        // Show a snackbar for unimplemented services
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Service coming soon!'),
            backgroundColor: AppColors.primary,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final double height = size.height;
    final services = _getServices();
    final double width = size.width;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          children: [
            Stack(children: [
             Container(
  height: height * 0.12,
  width: double.infinity,
  decoration: BoxDecoration(
    color: AppColors.white,
    border: Border.all(color: AppColors.white),
    borderRadius: BorderRadius.only(
      bottomLeft: Radius.circular(width * 0.15),
      bottomRight: Radius.circular(width * 0.15),
    ),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.1),
        spreadRadius: 2,
        blurRadius: 8,
        offset: Offset(0, 4), // Shadow position (x, y)
      ),
    ],
  ),
  child: Center(
    child: SafeArea(
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            colorFilter: ColorFilter.mode(
              AppColors.primary, // Change to any color and opacity
              BlendMode.srcATop, // Choose a blend mode that fits your effect
            ),
            fit: BoxFit.cover,
            image: AssetImage(AppImages.logo),
          ),
        ),
        height: context.responsiveHeight(0.22), // 22% of screen height
        width: context.responsiveWidth(0.72), // 72% of screen width
      ),
    ),
  ),
),
            ]),
            _buildServicesSection(width),
            _buildOfferBanners(width),
            _buildPopularDestinations(width),
            // _buildQuickActions(width),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildServicesSection(double width) {
    final services = _getServices();
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: width * 0.04,
        vertical: width * 0.03,
      ),
      child: GridView.count(
        crossAxisCount: 4,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
        childAspectRatio: 0.85,
        children: services.map((service) {
          return ModernServiceCard(
            svgPath: service['svgPath'],
            title: service['title'] ?? "",
            subtitle: service['subtitle'] ?? "",
            onTap: () => _navigateToService(
              service['id'] ?? "",
              service['route'] ?? "",
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildOfferBanners(double width) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _tr('search.special_offers'),
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all offers page
                },
                child: Text(
                  _tr('filters.viewMore'),
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 160,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _offerBanners.length,
              itemBuilder: (context, index) {
                final offer = _offerBanners[index];
                return Container(
                  width: width * 0.8,
                  margin: const EdgeInsets.only(right: 16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        offer['color'].withOpacity(0.8),
                        offer['color'].withOpacity(0.6),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: offer['color'].withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      )
                    ],
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        right: -20,
                        top: -20,
                        child: Icon(
                          offer['icon'],
                          size: 100,
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  offer['title'],
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  offer['subtitle'],
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 24,
                                    fontWeight: FontWeight.w900,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  offer['description'],
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.3),
                                    ),
                                  ),
                                  child: Text(
                                    'Code: ${offer['code']}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.arrow_forward,
                                    color: offer['color'],
                                    size: 16,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildPopularDestinations(double width) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _tr('search.popular_destinations'),
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all destinations
                },
                child: Text(
                  _tr('filters.viewMore'),
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 220,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _popularDestinations.length,
              itemBuilder: (context, index) {
                final destination = _popularDestinations[index];
                return Container(
                  width: 160,
                  margin: const EdgeInsets.only(right: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 120,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          ),
                          gradient: LinearGradient(
                            colors: [
                              Colors.blue.withOpacity(0.6),
                              Colors.purple.withOpacity(0.6),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Stack(
                          children: [
                            Center(
                              child: ClipRRect(
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(16),
                                  topRight: Radius.circular(16),
                                ),
                                child: Image.network(
                                  destination['image'],
                                  width: 160,
                                  height: 120,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      width: 160,
                                      height: 120,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(16),
                                          topRight: Radius.circular(16),
                                        ),
                                      ),
                                      child: Icon(
                                        Icons.image_not_supported,
                                        size: 40,
                                        color: Colors.grey[700],
                                      ),
                                    );
                                  },
                                  loadingBuilder: (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Container(
                                      width: 160,
                                      height: 120,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[200],
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(16),
                                          topRight: Radius.circular(16),
                                        ),
                                      ),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          value: loadingProgress.expectedTotalBytes != null
                                              ? loadingProgress.cumulativeBytesLoaded /
                                                  loadingProgress.expectedTotalBytes!
                                              : null,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.star,
                                      size: 12,
                                      color: Colors.amber,
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      destination['rating'].toString(),
                                      style: const TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              destination['name'],
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              destination['description'],
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Starting',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.grey[500],
                                  ),
                                ),
                                Text(
                                  destination['price'],
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

 

  Widget _buildBottomNavigation() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() => _currentIndex = index);
            switch (index) {
              case 0: // Home
                // Already on home, maybe scroll to top
                _scrollController.animateTo(
                  0,
                  duration: Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
                break;
              case 1: // Offers
                // Navigate to offers screen or show offers section
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Offers screen coming soon!'),
                    duration: Duration(seconds: 1),
                  ),
                );
                break;
              case 2:
                _showCurrencyDialog(context);
                break;
              case 3:
                _showLanguageDialog(context);
                break;
              case 4:
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => ProfileScreen()));
                break;
            }
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: AppColors.primary,
          unselectedItemColor: Colors.grey[400],
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.w600),
          elevation: 0,
          items: [
            BottomNavigationBarItem(
              icon: Icon(Icons.home_rounded),
              label: _tr('navigation.home'),
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.local_offer_rounded),
              label: _tr('navigation.offers'),
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.translate_outlined),
              label: _tr('navigation.language'),
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.currency_rupee_outlined),
              label: _tr('navigation.currency'),
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.person_rounded),
              label: _tr('navigation.profile'),
            ),
          ],
        ),
      ),
    );
  }
  
}

void _showLanguageDialog(context) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (_) => CurrencyBottomsheet(),
  );
}

void _showCurrencyDialog(context) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (_) => LanguageBottomsheet(),
  );
}

class ModernServiceCard extends StatelessWidget {
  final String svgPath;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;
  const ModernServiceCard({
    super.key,
    required this.svgPath,
    required this.title,
    required this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(width * 0.04),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(width * 0.04),
        child: Padding(
          padding: EdgeInsets.all(width * 0.02),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
             Padding(
  padding: EdgeInsets.all(width * 0.02),
  child: ClipRRect(
    borderRadius: BorderRadius.circular(width * 0.03),
    child: Image.asset(
      svgPath,
      width: width * 0.08,
      height: width * 0.08,
     fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Icon(
          Icons.image_not_supported,
          size: width * 0.045,
          color: Colors.white,
        );
      },
    ),
  ),
)
,
              SizedBox(height: width * 0.015),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: width * 0.025,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
