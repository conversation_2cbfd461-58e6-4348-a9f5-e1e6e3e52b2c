/// Authentication state management using Riverpod StateNotifier
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Authentication state class
class AuthState {
  final bool isLoading;
  final String? error;
  final bool isLoggedIn;
  final String? currentUserEmail;
  final String? currentUserPhone;
  final String? loginMethod; // 'email' or 'phone'
  final bool isInitializing;

  const AuthState({
    this.isLoading = false,
    this.error,
    this.isLoggedIn = false,
    this.currentUserEmail,
    this.currentUserPhone,
    this.loginMethod,
    this.isInitializing = true,
  });

  AuthState copyWith({
    bool? isLoading,
    String? error,
    bool? isLoggedIn,
    String? currentUserEmail,
    String? currentUserPhone,
    String? loginMethod,
    bool? isInitializing,
    bool clearEmail = false,
    bool clearPhone = false,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      currentUserEmail: clearEmail ? null : (currentUserEmail ?? this.currentUserEmail),
      currentUserPhone: clearPhone ? null : (currentUserPhone ?? this.currentUserPhone),
      loginMethod: loginMethod ?? this.loginMethod,
      isInitializing: isInitializing ?? this.isInitializing,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.isLoading == isLoading &&
        other.error == error &&
        other.isLoggedIn == isLoggedIn &&
        other.currentUserEmail == currentUserEmail &&
        other.currentUserPhone == currentUserPhone &&
        other.loginMethod == loginMethod &&
        other.isInitializing == isInitializing;
  }

  @override
  int get hashCode => Object.hash(
        isLoading,
        error,
        isLoggedIn,
        currentUserEmail,
        currentUserPhone,
        loginMethod,
        isInitializing,
      );
}

/// Authentication StateNotifier
class AuthNotifier extends StateNotifier<AuthState> {
  // SharedPreferences keys
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyUserEmail = 'user_email';
  static const String _keyUserPhone = 'user_phone';
  static const String _keyLoginMethod = 'login_method';
  static const String _keyUserName = 'user_name';
  static const String _keyProfileComplete = 'profile_complete';
  static const String _keyFirstTimeUser = 'first_time_user';
  static const String _keyUserProfiles = 'user_profiles'; // Store multiple user profiles

  AuthNotifier() : super(const AuthState()) {
    _loadAuthState();
  }

  /// Load authentication state from SharedPreferences
  Future<void> _loadAuthState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;
      final currentUserEmail = prefs.getString(_keyUserEmail);
      final currentUserPhone = prefs.getString(_keyUserPhone);
      final loginMethod = prefs.getString(_keyLoginMethod);

      state = state.copyWith(
        isLoggedIn: isLoggedIn,
        currentUserEmail: currentUserEmail,
        currentUserPhone: currentUserPhone,
        loginMethod: loginMethod,
        isInitializing: false,
      );
    } catch (e) {
      // Even if there's an error, mark initialization as complete
      state = state.copyWith(
        isInitializing: false,
        error: 'Error loading auth state: $e',
      );
    }
  }

  /// Save authentication state to SharedPreferences
  Future<void> _saveAuthState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyIsLoggedIn, state.isLoggedIn);
      
      if (state.currentUserEmail != null) {
        await prefs.setString(_keyUserEmail, state.currentUserEmail!);
      }
      
      if (state.currentUserPhone != null) {
        await prefs.setString(_keyUserPhone, state.currentUserPhone!);
      }
      
      if (state.loginMethod != null) {
        await prefs.setString(_keyLoginMethod, state.loginMethod!);
      }
    } catch (e) {
      state = state.copyWith(error: 'Error saving auth state: $e');
    }
  }

  /// Login with email
  Future<void> loginWithEmail(String email, String otp) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate login logic (replace with actual authentication)
      await Future.delayed(const Duration(seconds: 1));

      // For demo purposes, accept any email/otp combination
      state = state.copyWith(
        isLoading: false,
        isLoggedIn: true,
        currentUserEmail: email,
        loginMethod: 'email',
        clearPhone: true, // Clear phone when logging in with email
      );

      // Add to existing users list
      await _addToExistingUsers(email);

      await _saveAuthState();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Login failed: $e',
      );
    }
  }

  /// Login with phone
  Future<void> loginWithPhone(String phone, String otp) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate login logic (replace with actual authentication)
      await Future.delayed(const Duration(seconds: 1));

      // For demo purposes, accept any phone/otp combination
      state = state.copyWith(
        isLoading: false,
        isLoggedIn: true,
        currentUserPhone: phone,
        loginMethod: 'phone',
        clearEmail: true, // Clear email when logging in with phone
      );

      // Add to existing users list
      await _addToExistingUsers(phone);

      await _saveAuthState();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Login failed: $e',
      );
    }
  }

  /// Register with email
  Future<void> registerWithEmail(String email, String password, String name) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Simulate registration logic (replace with actual authentication)
      await Future.delayed(const Duration(seconds: 1));
      
      state = state.copyWith(
        isLoading: false,
        isLoggedIn: true,
        currentUserEmail: email,
        loginMethod: 'email',
      );
      
      await _saveAuthState();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed: $e',
      );
    }
  }

  /// Register with phone
  Future<void> registerWithPhone(String phone, String otp, String name) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Simulate registration logic (replace with actual authentication)
      await Future.delayed(const Duration(seconds: 1));
      
      state = state.copyWith(
        isLoading: false,
        isLoggedIn: true,
        currentUserPhone: phone,
        loginMethod: 'phone',
      );
      
      await _saveAuthState();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed: $e',
      );
    }
  }

  /// Check if user profile is complete
  Future<bool> isProfileComplete() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyProfileComplete) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Mark profile as complete
  Future<void> markProfileAsComplete() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyProfileComplete, true);
    } catch (e) {
      state = state.copyWith(error: 'Error marking profile complete: $e');
    }
  }

  /// Check if user is existing (has logged in before with this identifier)
  Future<bool> isExistingUser(String identifier) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userProfiles = prefs.getStringList(_keyUserProfiles) ?? [];
      return userProfiles.contains(identifier);
    } catch (e) {
      return false;
    }
  }

  /// Add user to existing users list
  Future<void> _addToExistingUsers(String identifier) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userProfiles = prefs.getStringList(_keyUserProfiles) ?? [];
      if (!userProfiles.contains(identifier)) {
        userProfiles.add(identifier);
        await prefs.setStringList(_keyUserProfiles, userProfiles);
      }
    } catch (e) {
      // Log error but don't throw
      print('Error adding user to existing users: $e');
    }
  }

  /// Get stored profile data for current user
  Future<Map<String, String?>> getStoredProfileData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'name': prefs.getString(_keyUserName),
        'email': state.currentUserEmail,
        'phone': state.currentUserPhone,
        'loginMethod': state.loginMethod,
      };
    } catch (e) {
      return {
        'name': null,
        'email': state.currentUserEmail,
        'phone': state.currentUserPhone,
        'loginMethod': state.loginMethod,
      };
    }
  }

  /// Determine navigation after successful login
  Future<String> getNavigationRoute({String loginSource = 'default'}) async {
    final isComplete = await isProfileComplete();
    final currentIdentifier = state.loginMethod == 'email'
        ? state.currentUserEmail
        : state.currentUserPhone;

    if (currentIdentifier != null) {
      final isExisting = await isExistingUser(currentIdentifier);

      if (isExisting && isComplete) {
        // Existing user with complete profile → Booking or Dashboard
        return loginSource == 'booking' ? '/booking-continue' : '/dashboard';
      } else {
        // New user OR existing user with incomplete profile → Progressive Onboarding
        return '/progressive-onboarding';
      }
    }

    // Fallback to progressive onboarding
    return '/progressive-onboarding';
  }

  /// Logout
  Future<void> logout() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final prefs = await SharedPreferences.getInstance();
      // Clear auth data but keep user profiles list for future reference
      await prefs.remove(_keyIsLoggedIn);
      await prefs.remove(_keyUserEmail);
      await prefs.remove(_keyUserPhone);
      await prefs.remove(_keyLoginMethod);
      await prefs.remove(_keyUserName);
      await prefs.remove(_keyProfileComplete);
      // Keep _keyUserProfiles for existing user detection

      state = const AuthState(isInitializing: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Logout failed: $e',
      );
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Authentication provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>(
  (ref) => AuthNotifier(),
);
