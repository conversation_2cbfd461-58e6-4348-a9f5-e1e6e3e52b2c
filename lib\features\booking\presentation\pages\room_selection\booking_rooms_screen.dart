import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/core/utils/extensions.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_details.dart';
import 'package:kind_ali/features/authentication/presentation/providers/auth_notifier.dart';
import 'package:kind_ali/features/booking/presentation/providers/room_selection_notifier.dart';
import 'package:kind_ali/features/authentication/presentation/pages/authentication/login_bottom_sheet.dart';
import 'package:kind_ali/features/booking/presentation/pages/booking/booking_screen.dart';
import 'package:kind_ali/features/booking/presentation/pages/room_selection/widget/roomcard_widget.dart';

class BookingRoomsScreen extends ConsumerStatefulWidget {
  final InventoryInfoList? hotel;
  final int? maxRoomCount;
  final int? totalGuests;
  
  const BookingRoomsScreen({
    super.key,
    this.hotel,
    this.maxRoomCount,
    this.totalGuests,
  });

  @override
  ConsumerState<BookingRoomsScreen> createState() => _BookingRoomsScreenState();
}

class _BookingRoomsScreenState extends ConsumerState<BookingRoomsScreen>
    with TickerProviderStateMixin {
  String selectedRoomType = 'Deluxe Room';
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final roomSelectionNotifier = ref.read(roomSelectionProvider.notifier);
      roomSelectionNotifier.loadHotelRoomsFromJson();

      // Set room limits from the passed parameters
      if (widget.maxRoomCount != null && widget.totalGuests != null) {
        roomSelectionNotifier.setRoomLimits(
          maxRoomCount: widget.maxRoomCount!,
          totalGuests: widget.totalGuests!,
        );
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  String _selectedSortOption = 'free_breakfast';
  final List<Map<String, dynamic>> _sortOptions = [
    {
      'id': 'free_breakfast',
      'label': 'itinerary.breakfastIncluded'.tr,
      'icon': Icons.free_breakfast
    },
    {
      'id': 'free_parking_wifi_breakfast',
      'label':
          '${'itinerary.freeParking'.tr},${'itinerary.freeWifi'.tr} & ${'itinerary.breakfastIncluded'.tr}',
      'icon': Icons.local_parking
    },
    {
      'id': 'breakfast',
      'label': 'itinerary.breakfastIncluded'.tr,
      'icon': Icons.breakfast_dining
    },
    {
      'id': 'bed_and_breakfast',
      'label': 'filters.rooms.title'.tr,
      'icon': Icons.hotel
    },
    {
      'id': 'free_parking_wifi_room',
      'label': '${'itinerary.freeParking'.tr} &${'itinerary.freeWifi'.tr}',
      'icon': Icons.wifi
    },
    {
      'id': 'room_only',
      'label': 'booking.roomOnly'.tr,
      'icon': Icons.bedroom_parent
    },
  ];

  Widget _buildSortOptionsBar() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.05, // 5% of screen width
        vertical: screenHeight * 0.015, // 1.5% of screen height
      ),
      child: Row(
        children: [
          Container(
            height: screenHeight * 0.06, // 6% of screen height
            width: screenHeight * 0.06, // Keep it square
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.primary,
            ),
            child: Icon(
              Icons.sort,
              color: Colors.white,
              size: screenHeight * 0.025, // 2.5% of screen height
            ),
          ),
          SizedBox(width: screenWidth * 0.025), // 2.5% of screen width
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _sortOptions.map((option) {
                  final bool isSelected = _selectedSortOption == option['id'];
                  return InkWell(
                    // onTap: () {
                    //   setState(() {
                    //     _selectedSortOption = option['id'] as String;
                    //   });
                    // },
                    onTap: () {
                      setState(() {
                        if (_selectedSortOption == option['id']) {
                          _selectedSortOption = 'all'; // or null if preferred
                        } else {
                          _selectedSortOption = option['id'] as String;
                        }
                      });
                    },

                    child: Container(
                      height: screenHeight * 0.06, // 6% of screen height
                      margin: EdgeInsets.only(
                          right: screenWidth * 0.025), // 2.5% of screen width
                      padding: EdgeInsets.symmetric(
                        horizontal: screenWidth * 0.02, // 3% of screen width
                        vertical: screenHeight * 0.008, // 1% of screen height
                      ),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary : Colors.white,
                        borderRadius: BorderRadius.circular(
                            screenWidth * 0.08), // 3% of screen width
                        border: Border.all(
                          color: isSelected
                              ? AppColors.secondary
                              : Colors.grey.shade300,
                        ),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: AppColors.secondary.withAlpha(51),
                                  blurRadius:
                                      screenWidth * 0.01, // 1% of screen width
                                  offset: Offset(
                                      0,
                                      screenHeight *
                                          0.0025), // 0.25% of screen height
                                ),
                              ]
                            : null,
                      ),
                      child: Row(
                        children: [
                          Icon(
                            option['icon'] as IconData,
                            size: screenHeight * 0.015, // 2% of screen height
                            color: isSelected
                                ? Colors.white
                                : Colors.grey.shade700,
                          ),
                          SizedBox(
                              width:
                                  screenWidth * 0.015), // 1.5% of screen width
                          Text(
                            option['label'] as String,
                            style: TextStyle(
                              fontSize:
                                  screenHeight * 0.014, // 1.6% of screen height
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isSelected
                                  ? Colors.white
                                  : Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final roomSelectionState = ref.watch(roomSelectionProvider);
    final roomSelectionNotifier = ref.read(roomSelectionProvider.notifier);

    return Scaffold(
          backgroundColor: AppColors.white,
          appBar: AppBar(
            title: Text('booking.roomsAvailable'.tr),
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(20),
              ),
            ),
          ),
          body: Column(
            children: [
              _buildSortOptionsBar(),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: MediaQuery.of(context).size.width *
                      0.04, // 5% of screen width
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Room limit indicator
                    Builder(
                      builder: (context) {
                        final screenWidth = MediaQuery.of(context).size.width;
                        final screenHeight = MediaQuery.of(context).size.height;

                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal:
                                screenWidth * 0.02, // 3% of screen width
                            vertical:
                                screenHeight * 0.0030, // 0.75% of screen height
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(26),
                            borderRadius: BorderRadius.circular(
                                screenWidth * 0.04), // 4% of screen width
                            border: Border.all(
                                color: AppColors.primary.withAlpha(51)),
                          ),
                          child: Text(
                            '${'search.travelers.rooms'.tr}: ${roomSelectionNotifier.getTotalSelectedRoomsCount()}/${roomSelectionState.maxRoomCount}',
                            style: TextStyle(
                              fontSize:
                                  screenHeight * 0.014, // 1.7% of screen height
                              fontWeight: FontWeight.w600,
                              color: AppColors.primary,
                            ),
                          ),
                        );
                      },
                    ),
                    // Clear filters
                    // InkWell(
                    //   onTap: () {
                    //     setState(() {
                    //       _selectedSortOption = 'all';
                    //     });
                    //   },
                    //   child: Container(
                    //     padding: EdgeInsets.symmetric(
                    //       horizontal: MediaQuery.of(context).size.width * 0.02,
                    //       vertical: MediaQuery.of(context).size.height * 0.008,
                    //     ),
                    //     child: Row(
                    //       mainAxisSize: MainAxisSize.min,
                    //       children: [
                    //         Icon(
                    //           Icons.filter_list_off,
                    //           size: MediaQuery.of(context).size.height * 0.022, // 2.2% of screen height
                    //           color: AppColors.secondary,
                    //         ),
                    //         SizedBox(
                    //           width: MediaQuery.of(context).size.width * 0.01, // 1% of screen width
                    //         ),
                    //         Text(
                    //           'filters.clear'.tr,
                    //           style: TextStyle(
                    //             decoration: TextDecoration.underline,
                    //             fontSize: MediaQuery.of(context).size.height * 0.02, // 2% of screen height
                    //             color: AppColors.secondary,
                    //             fontWeight: FontWeight.w500,
                    //           ),
                    //         ),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
              SizedBox(
                height: context.responsiveHeight(0.012), // 1.2% of screen height
              ),
              Expanded(
                child: ListView.separated(
                  itemCount: roomSelectionState.rooms.length,
                  itemBuilder: (context, index) => RoomcardWidget(
                    room: roomSelectionState.rooms[index],
                  ),
                  separatorBuilder: (context, index) => SizedBox(
                    height: MediaQuery.of(context).size.height *
                        0.025, // 2.5% of screen height
                  ),
                ),
              ),
              if (roomSelectionState.selectedRoomOptions.isNotEmpty)
                Container(
                  padding: EdgeInsets.all(MediaQuery.of(context).size.width *
                      0.05), // 5% of screen width
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.neutralDark.withAlpha(13),
                        blurRadius: MediaQuery.of(context).size.width *
                            0.025, // 2.5% of screen width
                        offset: Offset(
                            0,
                            -MediaQuery.of(context).size.height *
                                0.006), // -0.6% of screen height
                      ),
                    ],
                  ),
                  child: _buildBottomSection(roomSelectionState, roomSelectionNotifier),
                ),
            ],
          ),
        );
  }

  Widget _buildBottomSection(RoomSelectionState roomSelectionState, RoomSelectionNotifier roomSelectionNotifier) {
    final hasSelectedRooms = roomSelectionState.selectedRoomOptions.isNotEmpty;

    // Calculate room charges and taxes from the actual room options
    double roomCharges = 0.0;
    double taxesAndFees = 0.0;

    for (var selectedOption in roomSelectionState.selectedRoomOptions) {
      // Use the price from the SelectedRoomOption (which should include base fare)
      final baseFare = selectedOption.price;
      // For now, assume 18% tax (this should be configurable)
      final tax = baseFare * 0.18;

      // Multiply by quantity and add to totals
      roomCharges += baseFare * selectedOption.quantity;
      taxesAndFees += tax * selectedOption.quantity;
    }

    final totalWithTax = roomCharges + taxesAndFees;

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (_isExpanded)
          SizeTransition(
            sizeFactor: _animation,
            child: Column(
              children: [
                ...roomSelectionState.selectedRoomOptions
                    .map((selectedOption) => Container(
                          margin: EdgeInsets.only(bottom: screenHeight * 0.01),
                          padding: EdgeInsets.all(screenWidth * 0.03),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.02),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: isRTL
                                      ? CrossAxisAlignment.end
                                      : CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      selectedOption.roomType,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: screenHeight * 0.016,
                                      ),
                                    ),
                                    SizedBox(height: screenHeight * 0.0025),
                                    Text(
                                      'Room Option',
                                      style: TextStyle(
                                        fontSize: screenHeight * 0.014,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Column(
                                crossAxisAlignment: isRTL
                                    ? CrossAxisAlignment.start
                                    : CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'Qty: ${selectedOption.quantity}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: screenHeight * 0.014,
                                    ),
                                  ),
                                  SizedBox(height: screenHeight * 0.0025),
                                  Text(
                                    '₹${(selectedOption.price * selectedOption.quantity).toInt()}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: screenHeight * 0.016,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ))
                    .toList(),

                // Price breakdown section
                Container(
                  margin: EdgeInsets.only(
                      top: screenHeight * 0.01, bottom: screenHeight * 0.02),
                  padding: EdgeInsets.all(screenWidth * 0.03),
                  decoration: BoxDecoration(
                    color: AppColors.neutralLight,
                    borderRadius: BorderRadius.circular(screenWidth * 0.02),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    children: [
                      _buildPriceRow(
                        'Room Charges',
                        '₹${roomCharges.toInt()}',
                        screenHeight,
                        isRTL,
                      ),
                      SizedBox(height: screenHeight * 0.01),
                      _buildPriceRow(
                        'Taxes & Fees',
                        '₹${taxesAndFees.toInt()}',
                        screenHeight,
                        isRTL,
                      ),
                      Divider(height: screenHeight * 0.02),
                      _buildPriceRow(
                        'Total Amount',
                        '₹${totalWithTax.toInt()}',
                        screenHeight,
                        isRTL,
                        isTotal: true,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        Row(
          children: [
            InkWell(
              onTap: hasSelectedRooms ? _toggleExpansion : null,
              child: Row(
                children: [
                  Text(
                    '₹${totalWithTax.toInt()}',
                    style: TextStyle(
                      fontSize: screenHeight * 0.02,
                      fontWeight: FontWeight.bold,
                      color: AppColors.neutralDark,
                    ),
                  ),
                  SizedBox(width: screenWidth * 0.02),
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.expand_more,
                      size: screenHeight * 0.022,
                      color: AppColors.neutralDark,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: screenWidth * 0.04),
            Expanded(
              child: ElevatedButton(
                onPressed: hasSelectedRooms
                    ? () {
                        // Check if user is logged in
                        final authState = ref.read(authProvider);
                        if (!authState.isLoggedIn) {
                          // Show login bottom sheet if not logged in
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            isDismissible: true,
                            enableDrag: true,
                            builder: (context) => const LoginBottomSheet(),
                          ).then((_) {
                            // After login sheet is closed, check if user is logged in
                            final updatedAuthState = ref.read(authProvider);
                            if (updatedAuthState.isLoggedIn) {
                              // Navigate to booking screen if logged in
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => BookingScreen(
                                    hotel: widget.hotel,
                                    roomType: roomSelectionState.selectedRoomOptions.first
                                            .roomType,
                                    checkInDate: DateTime.now()
                                        .add(const Duration(days: 1)),
                                    checkOutDate: DateTime.now()
                                        .add(const Duration(days: 3)),
                                    adultCount: 2,
                                    childCount: 0,
                                    selectedRoomOptions:
                                        roomSelectionState.selectedRoomOptions,
                                    totalPrice:
                                        totalWithTax, // Pass total with tax
                                  ),
                                ),
                              );
                            }
                          });
                        } else {
                          // Navigate directly to booking screen if already logged in
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => BookingScreen(
                                hotel: widget.hotel,
                                roomType: roomSelectionState
                                        .selectedRoomOptions.first.roomType,
                                checkInDate:
                                    DateTime.now().add(const Duration(days: 1)),
                                checkOutDate:
                                    DateTime.now().add(const Duration(days: 3)),
                                adultCount: 2,
                                childCount: 0,
                                selectedRoomOptions:
                                    roomSelectionState.selectedRoomOptions,
                                totalPrice: totalWithTax, // Pass total with tax
                              ),
                            ),
                          );
                        }
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      hasSelectedRooms ? AppColors.primary : Colors.grey,
                  padding: EdgeInsets.symmetric(vertical: screenHeight * 0.018),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(screenWidth * 0.02),
                  ),
                ),
                child: Text(
                  hasSelectedRooms
                      ? 'booking.reserveRoom'.tr
                      : 'Select Rooms First',
                  style: TextStyle(
                    fontSize: screenHeight * 0.018,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),SizedBox(height: screenWidth * 0.04),
      ],
    );
  }

  // Helper method to build price rows
  Widget _buildPriceRow(
      String label, String amount, double screenHeight, bool isRTL,
      {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: screenHeight * (isTotal ? 0.016 : 0.015),
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: isTotal ? AppColors.neutralDark : Colors.grey.shade700,
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontSize: screenHeight * (isTotal ? 0.016 : 0.015),
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: isTotal ? AppColors.primary : AppColors.neutralDark,
          ),
        ),
      ],
    );
  }
}
