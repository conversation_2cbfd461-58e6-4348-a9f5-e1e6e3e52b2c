/// Authentication providers for Riverpod dependency injection
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/providers/core_providers.dart';
import '../../data/datasources/auth_local_datasource.dart';
import '../../data/datasources/auth_remote_datasource.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/auth/get_current_auth.dart';
import '../../domain/usecases/auth/is_logged_in.dart';
import '../../domain/usecases/auth/login_with_email.dart';
import '../../domain/usecases/auth/login_with_phone.dart';
import '../../domain/usecases/auth/logout.dart';
import '../../domain/usecases/auth/register_with_email.dart';
import '../../domain/usecases/auth/register_with_phone.dart';
import '../../domain/usecases/auth/send_otp.dart';
import '../../domain/usecases/auth/verify_otp.dart';

part 'auth_providers.g.dart';

// Data Sources
@riverpod
Future<AuthLocalDataSource> authLocalDataSource(Ref ref) async {
  final sharedPrefs = await ref.watch(sharedPreferencesProvider.future);
  return AuthLocalDataSourceImpl(sharedPreferences: sharedPrefs);
}

@riverpod
AuthRemoteDataSource authRemoteDataSource(Ref ref) {
  final client = ref.watch(httpClientProvider);
  return AuthRemoteDataSourceImpl(client: client);
}

// Repository
@riverpod
Future<AuthRepository> authRepository(Ref ref) async {
  final localDataSource = await ref.watch(authLocalDataSourceProvider.future);
  final remoteDataSource = ref.watch(authRemoteDataSourceProvider);
  final networkInfo = ref.watch(networkInfoProvider);

  return AuthRepositoryImpl(
    localDataSource: localDataSource,
    remoteDataSource: remoteDataSource,
    networkInfo: networkInfo,
  );
}

// Use Cases
@riverpod
Future<LoginWithEmail> loginWithEmail(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return LoginWithEmail(repository);
}

@riverpod
Future<LoginWithPhone> loginWithPhone(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return LoginWithPhone(repository);
}

@riverpod
Future<RegisterWithEmail> registerWithEmail(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return RegisterWithEmail(repository);
}

@riverpod
Future<RegisterWithPhone> registerWithPhone(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return RegisterWithPhone(repository);
}

@riverpod
Future<GetCurrentAuth> getCurrentAuth(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return GetCurrentAuth(repository);
}

@riverpod
Future<IsLoggedIn> isLoggedIn(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return IsLoggedIn(repository);
}

@riverpod
Future<SendOtp> sendOtp(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return SendOtp(repository);
}

@riverpod
Future<VerifyOtp> verifyOtp(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return VerifyOtp(repository);
}

@riverpod
Future<Logout> logout(Ref ref) async {
  final repository = await ref.watch(authRepositoryProvider.future);
  return Logout(repository);
}
