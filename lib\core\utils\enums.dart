/// Application-wide enums

/// Authentication states
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Loading states
enum LoadingState {
  initial,
  loading,
  loaded,
  error,
}

/// Booking status
enum BookingStatus {
  pending,
  confirmed,
  cancelled,
  completed,
}

/// Payment status
enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  refunded,
}

/// Room type
enum RoomType {
  single,
  double,
  suite,
  deluxe,
  presidential,
}

/// Sort options for hotels
enum HotelSortOption {
  priceAsc,
  priceDesc,
  rating,
  distance,
  popularity,
}

/// Filter options
enum FilterType {
  price,
  rating,
  amenities,
  location,
  roomType,
}

/// Language options
enum AppLanguage {
  english,
  arabic,
  french,
  spanish,
}

/// Currency options
enum AppCurrency {
  usd,
  eur,
  gbp,
  aed,
}

/// Network status
enum NetworkStatus {
  connected,
  disconnected,
  unknown,
}
