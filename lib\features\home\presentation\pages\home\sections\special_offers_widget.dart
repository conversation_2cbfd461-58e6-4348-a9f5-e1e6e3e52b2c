import 'package:flutter/material.dart';

class SpecialOffer {
  final String id;
  final String title;
  final String subtitle;
  final String description;
  final String promoCode;
  final String image;

  SpecialOffer({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.promoCode,
    required this.image,
  });
}

class SpecialOffersWidget extends StatelessWidget {
  // Promotional banner style offers - exact match to your original data
  final List<SpecialOffer> specialOffers = [
    SpecialOffer(
      id: "1",
      title: "Up to 25% Off",
      subtitle: "On Domestic Hotels",
      description: "*Offers is Valid On UPI Transactions Only",
      promoCode: "YTUPI",
      image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Kerala.jpg",
    ),
    SpecialOffer(
      id: "2",
      title: "Up to 35% OFF",
      subtitle: "On International Hotels",
      description: "*Offers is Valid Only On Confirmed Hotel Bookings",
      promoCode: "YTICICEMI",
      image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/goa.jpg",
    ),
    SpecialOffer(
      id: "3",
      title: "Up to 30% Off",
      subtitle: "On Weekend Bookings",
      description: "*Offer Valid On Axis Bank Credit Card EMI Transactions Only",
      promoCode: "YRAXISEMI",
      image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Dubai.png",
    ),
    SpecialOffer(
      id: "4",
      title: "Up to 40% OFF",
      subtitle: "On Luxury Resorts",
      description: "*Valid on HDFC Bank Debit & Credit Cards",
      promoCode: "YHDFC40",
      image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Maldives.png",
    ),
    SpecialOffer(
      id: "5",
      title: "Up to 20% Off",
      subtitle: "On Hill Station Hotels",
      description: "*Weekend Special Offer Valid Till Sunday",
      promoCode: "WEEKEND20",
      image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Himachal.png",
    ),
  ];

  SpecialOffersWidget({Key? key}) : super(key: key);

  void _handleOfferClick(SpecialOffer offer) {
    print("Clicked special offer: ${offer.title}");
    // Implement navigation to search with offer applied
  }

  void _handleViewDetails(SpecialOffer offer) {
    print("View details for: ${offer.promoCode}");
    // Implement view details logic
  }

  @override
  Widget build(BuildContext context) {
    if (specialOffers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 1100),
      margin: const EdgeInsets.symmetric(vertical: 40, horizontal: 20),
      child: Column(
        children: [
      

       
          SizedBox(
            height: 140,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: specialOffers.length,
              itemBuilder: (context, index) {
                final offer = specialOffers[index];
                return Padding(
                  padding: EdgeInsets.only(
                    left: index == 0 ? 4 : 10,
                    right: index == specialOffers.length - 1 ? 4 : 0,
                  ),
                  child: _buildOfferCard(offer),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOfferCard(SpecialOffer offer) {
    return GestureDetector(
      onTap: () => _handleOfferClick(offer),
      child: Container(
        width: 400,
        height: 140,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 16,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // Image section with curved overlay
            SizedBox(
              width: 140,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      bottomLeft: Radius.circular(16),
                    ),
                    child: Image.network(
                      offer.image,
                      width: 140,
                      height: 140,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 140,
                          height: 140,
                          color: Colors.grey[300],
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                          ),
                        );
                      },
                    ),
                  ),
                  // Curved overlay effect
                  Positioned(
                    right: -20,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: 60,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(50),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Content section
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Title and description
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            offer.title,
                            style: const TextStyle(
                              fontSize: 19,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1A1A1A),
                              height: 1.25,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 5),
                          Text(
                            offer.subtitle,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Color(0xFF555555),
                              fontWeight: FontWeight.w500,
                              height: 1.3,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 7),
                          Text(
                            offer.description,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Color(0xFF777777),
                              height: 1.4,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    
                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFFFF6B6B), Color(0xFFFF5252)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(20),
                              onTap: () => _handleOfferClick(offer),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                child: Text(
                                  offer.promoCode,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () => _handleViewDetails(offer),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'View Details',
                                style: TextStyle(
                                  color: Color(0xFF007BFF),
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(width: 4),
                              Icon(
                                Icons.chevron_right,
                                color: Color(0xFF007BFF),
                                size: 16,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Example usage in a screen
class SpecialOffersScreen extends StatelessWidget {
  const SpecialOffersScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Center(
          child: SpecialOffersWidget(),
        ),
      ),
    );
  }
}