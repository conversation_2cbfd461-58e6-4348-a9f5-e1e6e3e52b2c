﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/user_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for getting current user
class GetCurrentUser implements UseCaseNoParams<UserEntity> {
  final UserRepository repository;

  GetCurrentUser(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call() async {
    return await repository.getUserProfile();
  }
}

