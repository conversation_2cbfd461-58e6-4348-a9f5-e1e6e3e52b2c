/// Providers for splash feature data layer
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/core_providers.dart';
import '../../domain/repositories/splash_repository.dart';
import '../../domain/usecases/check_authentication_status.dart';
import '../../domain/usecases/check_onboarding_status.dart';
import '../../domain/usecases/initialize_app_data.dart';
import '../datasources/splash_local_datasource.dart';
import '../repositories/splash_repository_impl.dart';

// Data Sources
final splashLocalDataSourceProvider = FutureProvider<SplashLocalDataSource>((ref) async {
  final sharedPrefs = await ref.watch(sharedPreferencesProvider.future);
  return SplashLocalDataSourceImpl(sharedPrefs);
});

// Repository
final splashRepositoryProvider = FutureProvider<SplashRepository>((ref) async {
  final localDataSource = await ref.watch(splashLocalDataSourceProvider.future);
  return SplashRepositoryImpl(localDataSource: localDataSource);
});

// Use Cases
final checkAuthenticationStatusProvider = FutureProvider<CheckAuthenticationStatus>((ref) async {
  final repository = await ref.watch(splashRepositoryProvider.future);
  return CheckAuthenticationStatus(repository);
});

final checkOnboardingStatusProvider = FutureProvider<CheckOnboardingStatus>((ref) async {
  final repository = await ref.watch(splashRepositoryProvider.future);
  return CheckOnboardingStatus(repository);
});

final initializeAppDataProvider = FutureProvider<InitializeAppData>((ref) async {
  final repository = await ref.watch(splashRepositoryProvider.future);
  return InitializeAppData(repository);
});
