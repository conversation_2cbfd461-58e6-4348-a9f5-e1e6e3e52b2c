﻿import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/core/constants/app_constants.dart';
import '../../../../core/error/exceptions.dart';
import 'package:kind_ali/core/error/failures.dart';
import 'package:kind_ali/core/network/network_info.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/user_repository.dart';
import '../models/user_model.dart';

/// Implementation of user repository
class UserRepositoryImpl implements UserRepository {
  final SharedPreferences sharedPreferences;
  final NetworkInfo networkInfo;

  UserRepositoryImpl({
    required this.sharedPreferences,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, UserEntity>> getUserProfile() async {
    try {
      final userJson = sharedPreferences.getString(AppConstants.keyUserProfile);
      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        final userModel = UserModel.fromJson(userMap);
        return Right(userModel.toEntity());
      }
      return Left(const CacheFailure('No user profile found'));
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateUserProfile({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    DateTime? dateOfBirth,
    String? nationality,
    String? passportNumber,
    String? preferredLanguage,
    String? preferredCurrency,
  }) async {
    try {
      // Get current user first
      final currentUserResult = await getUserProfile();
      return currentUserResult.fold(
        (failure) => Left(failure),
        (currentUser) async {
          final updatedUser = UserModel.fromEntity(currentUser).copyWith(
            firstName: firstName ?? currentUser.firstName,
            lastName: lastName ?? currentUser.lastName,
            email: email ?? currentUser.email,
            phone: phone ?? currentUser.phone,
            updatedAt: DateTime.now(),
          );

          await sharedPreferences.setString(
            AppConstants.keyUserProfile,
            json.encode(updatedUser.toJson()),
          );

          return Right(updatedUser.toEntity());
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, String>> uploadProfileImage(String imagePath) async {
    try {
      // For local implementation, simulate upload and return a mock URL
      await Future.delayed(const Duration(seconds: 1));
      final mockImageUrl = 'local://profile_images/${DateTime.now().millisecondsSinceEpoch}.jpg';
      return Right(mockImageUrl);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateProfileImage(String imageUrl) async {
    try {
      final currentUserResult = await getUserProfile();
      return currentUserResult.fold(
        (failure) => Left(failure),
        (currentUser) async {
          final updatedUser = UserModel.fromEntity(currentUser).copyWith(
            updatedAt: DateTime.now(),
          );

          await sharedPreferences.setString(
            AppConstants.keyUserProfile,
            json.encode(updatedUser.toJson()),
          );

          return Right(updatedUser.toEntity());
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> verifyEmail(String verificationToken) async {
    try {
      // For local implementation, simulate email verification
      await Future.delayed(const Duration(seconds: 1));

      final currentUserResult = await getUserProfile();
      return currentUserResult.fold(
        (failure) => Left(failure),
        (currentUser) async {
          final updatedUser = UserModel.fromEntity(currentUser).copyWith(
            isEmailVerified: true,
            updatedAt: DateTime.now(),
          );

          await sharedPreferences.setString(
            AppConstants.keyUserProfile,
            json.encode(updatedUser.toJson()),
          );

          return const Right(null);
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> verifyPhone({
    required String phone,
    required String otp,
  }) async {
    try {
      // For local implementation, simulate phone verification
      await Future.delayed(const Duration(seconds: 1));

      final currentUserResult = await getUserProfile();
      return currentUserResult.fold(
        (failure) => Left(failure),
        (currentUser) async {
          final updatedUser = UserModel.fromEntity(currentUser).copyWith(
            phone: phone,
            isPhoneVerified: true,
            updatedAt: DateTime.now(),
          );

          await sharedPreferences.setString(
            AppConstants.keyUserProfile,
            json.encode(updatedUser.toJson()),
          );

          return const Right(null);
        },
      );
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // For local implementation, simulate password change
      await Future.delayed(const Duration(seconds: 1));
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount() async {
    try {
      await sharedPreferences.remove(AppConstants.keyUserProfile);
      await sharedPreferences.remove(AppConstants.keyAuthToken);
      await sharedPreferences.remove(AppConstants.keyRefreshToken);
      await sharedPreferences.setBool(AppConstants.keyIsLoggedIn, false);
      return const Right(null);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, UserPreferencesEntity>> getUserPreferences() async {
    try {
      final language = sharedPreferences.getString(AppConstants.keyLanguage) ?? AppConstants.defaultLanguage;
      final currency = sharedPreferences.getString(AppConstants.keyCurrency) ?? AppConstants.defaultCurrency;

      final preferences = UserPreferencesEntity(
        language: language,
        currency: currency,
        emailNotifications: sharedPreferences.getBool('email_notifications') ?? true,
        pushNotifications: sharedPreferences.getBool('push_notifications') ?? true,
        smsNotifications: sharedPreferences.getBool('sms_notifications') ?? false,
      );

      return Right(preferences);
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, UserPreferencesEntity>> updateUserPreferences({
    String? language,
    String? currency,
    bool? emailNotifications,
    bool? pushNotifications,
    bool? smsNotifications,
  }) async {
    try {
      await Future.wait([
        if (language != null) sharedPreferences.setString(AppConstants.keyLanguage, language),
        if (currency != null) sharedPreferences.setString(AppConstants.keyCurrency, currency),
        if (emailNotifications != null) sharedPreferences.setBool('email_notifications', emailNotifications),
        if (pushNotifications != null) sharedPreferences.setBool('push_notifications', pushNotifications),
        if (smsNotifications != null) sharedPreferences.setBool('sms_notifications', smsNotifications),
      ]);

      return await getUserPreferences();
    } catch (e) {
      return Left(_mapExceptionToFailure(e));
    }
  }




  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is ServerException) {
      return ServerFailure(exception.message);
    } else if (exception is CacheException) {
      return CacheFailure(exception.message);
    } else if (exception is NetworkException) {
      return NetworkFailure(exception.message);
    } else {
      return GeneralFailure('An unexpected error occurred: $exception');
    }
  }
}
