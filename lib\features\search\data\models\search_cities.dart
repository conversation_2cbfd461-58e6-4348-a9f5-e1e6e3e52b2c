import '../../domain/entities/search_entity.dart';

class SearchCitiesData {
  final List<SearchCity> cities;
  final List<PopularPlace> popularPlaces;

  SearchCitiesData({
    required this.cities,
    required this.popularPlaces,
  });

  factory SearchCitiesData.fromJson(Map<String, dynamic> json) {
    return SearchCitiesData(
      cities: (json['cities'] as List<dynamic>)
          .map((cityJson) => SearchCity.fromJson(cityJson))
          .toList(),
      popularPlaces: (json['popularPlaces'] as List<dynamic>)
          .map((placeJson) => PopularPlace.fromJson(placeJson))
          .toList(),
    );
  }
}

class SearchCity {
  final String name;
  final int propertyCount;
  final String countryCode;
  final String country;

  SearchCity({
    required this.name,
    required this.propertyCount,
    required this.countryCode,
    required this.country,
  });

  factory SearchCity.fromJson(Map<String, dynamic> json) {
    return SearchCity(
      name: json['name'] ?? '',
      propertyCount: json['propertyCount'] ?? 0,
      countryCode: json['countryCode'] ?? '',
      country: json['country'] ?? '',
    );
  }

  // Helper method to get flag emoji from country code
  String get flagEmoji {
    switch (countryCode.toLowerCase()) {
      case 'in':
        return '🇮🇳';
      case 'ae':
        return '🇦🇪';
      case 'gb':
        return '🇬🇧';
      case 'fr':
        return '🇫🇷';
      case 'us':
        return '🇺🇸';
      case 'sg':
        return '🇸🇬';
      case 'th':
        return '🇹🇭';
      case 'jp':
        return '🇯🇵';
      case 'au':
        return '🇦🇺';
      case 'es':
        return '🇪🇸';
      case 'it':
        return '🇮🇹';
      case 'de':
        return '🇩🇪';
      case 'cn':
        return '🇨🇳';
      case 'br':
        return '🇧🇷';
      case 'ca':
        return '🇨🇦';
      case 'mx':
        return '🇲🇽';
      default:
        return '🌍';
    }
  }
}

class PopularPlace {
  final String name;
  final String city;
  final int propertyCount;
  final String countryCode;
  final String country;

  PopularPlace({
    required this.name,
    required this.city,
    required this.propertyCount,
    required this.countryCode,
    required this.country,
  });

  factory PopularPlace.fromJson(Map<String, dynamic> json) {
    return PopularPlace(
      name: json['name'] ?? '',
      city: json['city'] ?? '',
      propertyCount: json['propertyCount'] ?? 0,
      countryCode: json['countryCode'] ?? '',
      country: json['country'] ?? '',
    );
  }

  // Helper method to get flag emoji from country code
  String get flagEmoji {
    switch (countryCode.toLowerCase()) {
      case 'in':
        return '🇮🇳';
      case 'ae':
        return '🇦🇪';
      case 'gb':
        return '🇬🇧';
      case 'fr':
        return '🇫🇷';
      case 'us':
        return '🇺🇸';
      case 'sg':
        return '🇸🇬';
      case 'th':
        return '🇹🇭';
      case 'jp':
        return '🇯🇵';
      case 'au':
        return '🇦🇺';
      case 'es':
        return '🇪🇸';
      case 'it':
        return '🇮🇹';
      case 'de':
        return '🇩🇪';
      case 'cn':
        return '🇨🇳';
      case 'br':
        return '🇧🇷';
      case 'ca':
        return '🇨🇦';
      case 'mx':
        return '🇲🇽';
      default:
        return '🌍';
    }
  }
}

// SearchCities model class for data source compatibility
class SearchCities {
  final String? cityId;
  final String? cityName;
  final String? countryName;
  final String? countryCode;
  final int? propertyCount;
  final String? imageUrl;
  final bool? isPopular;
  final double? latitude;
  final double? longitude;

  SearchCities({
    this.cityId,
    this.cityName,
    this.countryName,
    this.countryCode,
    this.propertyCount,
    this.imageUrl,
    this.isPopular,
    this.latitude,
    this.longitude,
  });

  factory SearchCities.fromJson(Map<String, dynamic> json) {
    return SearchCities(
      cityId: json['cityId']?.toString(),
      cityName: json['name'] ?? json['cityName'],
      countryName: json['country'] ?? json['countryName'],
      countryCode: json['countryCode'],
      propertyCount: json['propertyCount'],
      imageUrl: json['imageUrl'],
      isPopular: json['isPopular'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cityId': cityId,
      'name': cityName,
      'country': countryName,
      'countryCode': countryCode,
      'propertyCount': propertyCount,
      'imageUrl': imageUrl,
      'isPopular': isPopular,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  // Convert to entity
  CityEntity toEntity() {
    return CityEntity(
      cityId: cityId,
      cityName: cityName,
      countryName: countryName,
      countryCode: countryCode,
      propertyCount: propertyCount,
      imageUrl: imageUrl,
      isPopular: isPopular,
      latitude: latitude,
      longitude: longitude,
    );
  }
}
