import 'package:flutter/material.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_details.dart';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_detail/widgets/hotel_map_widget.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class LocationTab extends StatelessWidget {
  final InventoryInfoList? hotel;

  const LocationTab({
    Key? key,
    this.hotel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('hotel.detail.location'.tr),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: HotelMapWidget(
            hotel: hotel,
            height: 200,
            showFullScreenButton: false,
          ),
        ),
        const SizedBox(height: 16),
        _buildExploreArea(),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildExploreArea() {
    final nearbyPlaces = [
      {'name': 'Candolim Beach', 'distance': '0.2 km', 'icon': Icons.beach_access},
      {'name': 'Calangute Beach', 'distance': '2.1 km', 'icon': Icons.beach_access},
      {'name': 'Casino Palms', 'distance': '1.8 km', 'icon': Icons.casino},
      {'name': 'Fort Aguada', 'distance': '3.5 km', 'icon': Icons.location_city},
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'hotel.detail.nearbyPlaces'.tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...nearbyPlaces.map((place) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      place['icon'] as IconData,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        place['name'] as String,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        place['distance'] as String,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}