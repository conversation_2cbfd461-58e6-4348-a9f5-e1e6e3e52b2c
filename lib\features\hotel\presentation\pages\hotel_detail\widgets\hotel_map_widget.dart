import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geocoding/geocoding.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_details.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class HotelMapWidget extends StatefulWidget {
  final InventoryInfoList? hotel;
  final double height;
  final double? width;
  final bool showFullScreenButton;
  final VoidCallback? onFullScreenTap;
  final BorderRadius? borderRadius;

  // Default coordinates for Goa, India if hotel coordinates are not available
  static const double defaultLatitude = 15.2993;
  static const double defaultLongitude = 74.1240;
  static const double defaultZoom = 13.0;

  const HotelMapWidget({
    super.key,
    this.hotel,
    this.height = 200,
    this.width,
    this.showFullScreenButton = true,
    this.onFullScreenTap,
    this.borderRadius,
  });

  @override
  State<HotelMapWidget> createState() => _HotelMapWidgetState();
}

class _HotelMapWidgetState extends State<HotelMapWidget> {
  late double latitude;
  late double longitude;
  late String hotelName;
  late String hotelAddress;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeMapData();
  }

  Future<void> _initializeMapData() async {
    hotelName = widget.hotel?.name ?? 'Hotel Location';
    hotelAddress = widget.hotel?.locality ?? '';

    // First check if we have coordinates in the hotel data
    if (widget.hotel?.geoLocationInfo!.lat != null &&
        widget.hotel?.geoLocationInfo!.lon != null) {
      latitude = widget.hotel!.geoLocationInfo!.lat!;
      longitude = widget.hotel!.geoLocationInfo!.lon!;
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
      return;
    }

    // If no coordinates but we have an address, try to geocode it
    if (hotelAddress.isNotEmpty) {
      try {
        final locations = await locationFromAddress(hotelAddress);
        if (locations.isNotEmpty) {
          latitude = locations.first.latitude;
          longitude = locations.first.longitude;
          if (mounted) {
            setState(() {
              isLoading = false;
            });
          }
          return;
        }
      } catch (e) {
        debugPrint('Error geocoding address: $e');
      }
    }

    // If all else fails, use default coordinates
    latitude = HotelMapWidget.defaultLatitude;
    longitude = HotelMapWidget.defaultLongitude;
    if (mounted) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(
        height: widget.height,
        width: widget.width,
        decoration: BoxDecoration(
          borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
          color: Colors.grey[200],
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      height: widget.height,
      width: widget.width,
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        children: [
          FlutterMap(
            options: MapOptions(
              initialCenter: LatLng(latitude, longitude),
              initialZoom: HotelMapWidget.defaultZoom,
            ),
            children: [
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.example.kind_ali',
              ),
              MarkerLayer(
                markers: [
                  Marker(
                    point: LatLng(latitude, longitude),
                    width: 40,
                    height: 40,
                    child: _buildMarker(),
                  ),
                ],
              ),
            ],
          ),
          if (widget.showFullScreenButton)
            Positioned(
              top: 10,
              right: 10,
              child: GestureDetector(
                onTap: widget.onFullScreenTap,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(26),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.fullscreen,
                    color: Colors.black87,
                    size: 20,
                  ),
                ),
              ),
            ),
          Positioned(
            bottom: 10,
            right: 10,
            child: GestureDetector(
              onTap: () => _launchMapsUrl(latitude, longitude, hotelName),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.directions,
                      color: Theme.of(context).primaryColor,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Directions',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMarker() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(51),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Icon(
            Icons.location_on,
            color: Colors.red,
            size: 30,
          ),
        ),
        Container(
          width: 8,
          height: 8,
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
        ),
      ],
    );
  }

  Future<void> _launchMapsUrl(double latitude, double longitude, String label) async {
    final url = Uri.parse(
      'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude&query_place_id=$label',
    );

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }
}
