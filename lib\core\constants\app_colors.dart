import 'package:flutter/material.dart';

class AppColors {
  // Main color scheme based on the blue colors provided
  static const primary = Color(0xFF003b95);     // Navy Blue - Primary color for main elements, app bars, and buttons
  static const secondary = Color(0xFF3333FF);   // Bright Blue - Secondary color for highlights, active elements, and accents
  static const tertiary = Color(0xFF0033CC);    // Medium Blue - Tertiary color for additional elements and hover states
  static const quaternary = Color(0xFF002299);  // Deep Blue - Quaternary color for depth and secondary backgrounds
  static const quinary = Color(0xFF001166);     // Dark Navy - Quinary color for dark elements and text on light backgrounds

  // Supporting colors
  static const white = Color(0xFFFFFFFF);       // White - For backgrounds and text on dark backgrounds
  static const neutralLight = Color(0xFFF0F4FF); // Light Blue-tinted neutral for backgrounds and subtle elements
  static const neutralDark = Color(0xFF1A1A40);  // Dark blue-tinted neutral for text and icons
  static const accent = Color(0xFF00CCFF);       // Cyan - Accent color for call-to-action buttons and highlights

  // Functional colors
  static const background = white;              // Default background color
  static const surface = Color(0xFFF5F9FF);     // Slightly blue-tinted surface for cards and containers
  static const error = Color(0xFFD32F2F);       // Error color for alerts and error messages
  static const success = Color(0xFF4CAF50);     // Success color for confirmations
  static const warning = Color(0xFFFFC107);
  static const yellowshade = Color(0xFFFFEBEB);     // Warning color for alerts
       // Warning color for alerts
  static const info = Color(0xFF2196F3);        // Info color for informational messages

  // Text colors
  static const text = neutralDark;              // Main text color
  static const textLight = Color(0xFF4D4D80);   // Secondary text color (blue-tinted gray)
  static const textOnPrimary = white;           // Text color on primary background
  static const textOnSecondary = white;         // Text color on secondary background

  // UI element colors
  static const divider = Color(0xFFD6E0FF);     // Divider color that complements the blue scheme
  static const shadow = Color(0x1A000080);      // Shadow color for elevation (10% opacity navy blue)
  static const overlay = Color(0x33000080);     // Overlay color for modals (20% opacity navy blue)

  // Gradient colors
  static const gradientStart = secondary;       // Start color for gradients
  static const gradientEnd = primary;           // End color for gradients

  // Transparency helpers
  static Color primaryWithAlpha(int alpha) {
    return primary.withAlpha(alpha);
  }

  static Color secondaryWithAlpha(int alpha) {
    return secondary.withAlpha(alpha);
  }

  // Helper to convert opacity (0.0-1.0) to alpha (0-255)
  static int opacityToAlpha(double opacity) {
    return (opacity * 255).round();
  }

  // Material color swatch for the primary color (Navy Blue)
  static const MaterialColor primaryMaterialColor = MaterialColor(
    0xFF000080, // Primary color value
    <int, Color>{
      50: Color(0xFFE6E6F0),
      100: Color(0xFFBFBFD9),
      200: Color(0xFF9999C2),
      300: Color(0xFF7373AB),
      400: Color(0xFF4D4D94),
      500: Color(0xFF000080), // Primary color
      600: Color(0xFF000073),
      700: Color(0xFF000066),
      800: Color(0xFF000059),
      900: Color(0xFF00004D),
    },
  );

  // Material color swatch for the secondary color (Bright Blue)
  static const MaterialColor secondaryMaterialColor = MaterialColor(
    0xFF3333FF, // Secondary color value
    <int, Color>{
      50: Color(0xFFE6E6FF),
      100: Color(0xFFBFBFFF),
      200: Color(0xFF9999FF),
      300: Color(0xFF7373FF),
      400: Color(0xFF4D4DFF),
      500: Color(0xFF3333FF), // Secondary color
      600: Color(0xFF2E2EE6),
      700: Color(0xFF2929CC),
      800: Color(0xFF2424B3),
      900: Color(0xFF1F1F99),
    },
  );
}
