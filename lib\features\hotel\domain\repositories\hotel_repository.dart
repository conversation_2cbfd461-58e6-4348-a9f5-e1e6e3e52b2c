﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../entities/hotel_entity.dart';
import '../entities/room_entity.dart';
import 'package:kind_ali/features/search/domain/entities/search_entity.dart';

/// Abstract repository for hotel-related operations
abstract class HotelRepository {
  /// Get all hotels
  Future<Either<Failure, List<HotelEntity>>> getHotels();

  /// Get hotel details by ID
  Future<Either<Failure, HotelEntity>> getHotelDetails(int hotelId);

  /// Search hotels based on criteria
  Future<Either<Failure, List<HotelEntity>>> searchHotels(
    HotelSearchEntity searchCriteria,
  );

  /// Get nearby hotels based on location
  Future<Either<Failure, List<HotelEntity>>> getNearbyHotels({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  });

  /// Get hotel rooms by hotel ID
  Future<Either<Failure, List<RoomEntity>>> getHotelRooms(int hotelId);

  /// Get filtered hotels
  Future<Either<Failure, List<HotelEntity>>> getFilteredHotels({
    double? minPrice,
    double? maxPrice,
    double? minRating,
    List<String>? amenities,
    String? sortBy,
  });

  /// Get popular hotels
  Future<Either<Failure, List<HotelEntity>>> getPopularHotels({
    int limit = 10,
  });

  /// Add hotel to wishlist
  Future<Either<Failure, void>> addToWishlist(int hotelId);

  /// Remove hotel from wishlist
  Future<Either<Failure, void>> removeFromWishlist(int hotelId);

  /// Get wishlist hotels
  Future<Either<Failure, List<HotelEntity>>> getWishlistHotels();
}
