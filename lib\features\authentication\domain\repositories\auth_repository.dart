﻿import 'package:dartz/dartz.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../entities/user_entity.dart';

/// Abstract repository for authentication-related operations
abstract class AuthRepository {
  /// Login with email and password
  Future<Either<Failure, AuthEntity>> loginWithEmail({
    required String email,
    required String password,
  });

  /// Login with phone and OTP
  Future<Either<Failure, AuthEntity>> loginWithPhone({
    required String phone,
    required String otp,
  });

  /// Register new user with email
  Future<Either<Failure, AuthEntity>> registerWithEmail({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  });

  /// Register new user with phone
  Future<Either<Failure, AuthEntity>> registerWithPhone({
    required String firstName,
    required String lastName,
    required String phone,
  });

  /// Send OTP to phone number
  Future<Either<Failure, void>> sendOtp(String phone);

  /// Verify OTP
  Future<Either<Failure, bool>> verifyOtp({
    required String phone,
    required String otp,
  });

  /// Logout user
  Future<Either<Failure, void>> logout();

  /// Refresh authentication token
  Future<Either<Failure, AuthEntity>> refreshToken();

  /// Forgot password
  Future<Either<Failure, void>> forgotPassword(String email);

  /// Reset password
  Future<Either<Failure, void>> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  });

  /// Get current authentication status
  Future<Either<Failure, AuthEntity?>> getCurrentAuth();

  /// Check if user is logged in
  Future<Either<Failure, bool>> isLoggedIn();

  /// Save authentication data locally
  Future<Either<Failure, void>> saveAuthData(AuthEntity authEntity);

  /// Clear authentication data locally
  Future<Either<Failure, void>> clearAuthData();
}
