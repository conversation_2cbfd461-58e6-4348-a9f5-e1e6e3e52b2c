import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_details.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_rooms.dart';
import 'package:kind_ali/features/booking/presentation/providers/room_selection_notifier.dart';

class RoomOptionsWidget extends ConsumerStatefulWidget {
  final InventoryInfoList? hotel;
  final Room room;
  final RoomOption roomOption;

  const RoomOptionsWidget({
    super.key,
    required this.room,
    required this.roomOption,
    this.hotel,
  });

  @override
  ConsumerState<RoomOptionsWidget> createState() => _RoomOptionsWidgetState();
}

class _RoomOptionsWidgetState extends ConsumerState<RoomOptionsWidget> {

  /// Get the quantity of this specific room option
  int _getRoomOptionQuantity(RoomSelectionState state) {
    final selectedOption = state.selectedRoomOptions.firstWhere(
      (option) => option.roomId == widget.room.id &&
                  option.roomOption.blockId == widget.roomOption.blockId,
      orElse: () => SelectedRoomOption(
        roomId: widget.room.id ?? '',
        roomType: widget.room.name ?? '',
        price: (widget.roomOption.fareDetail?.displayedBaseFare ?? 0.0).toDouble(),
        room: widget.room,
        roomOption: widget.roomOption,
        quantity: 0,
      ),
    );
    return selectedOption.quantity;
  }

  /// Check if we can add more rooms
  bool _canAddRoom(RoomSelectionState state) {
    final totalRooms = state.selectedRoomOptions.fold(0, (sum, option) => sum + option.quantity);
    return totalRooms < state.maxRoomCount;
  }

  /// Add room option
  void _addRoomOption(RoomSelectionNotifier notifier) {
    final selectedOption = SelectedRoomOption(
      roomId: widget.room.id ?? '',
      roomType: widget.room.name ?? '',
      price: (widget.roomOption.fareDetail?.displayedBaseFare ?? 0.0).toDouble(),
      room: widget.room,
      roomOption: widget.roomOption,
      quantity: 1,
    );
    notifier.addSelectedRoomOption(selectedOption);
  }

  /// Remove room option
  void _removeRoomOption(RoomSelectionNotifier notifier) {
    notifier.removeSelectedRoomOption(widget.room.id ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 220,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFe2e8f0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.roomOption.name ?? 'Room Option',
            style: const TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 16,
              color: Color(0xFF1a1a1a),
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8), // Reduced spacing
          
          // Wrap content in a Flexible widget to allow scrolling if needed
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBenefitsTags(),
              const SizedBox(height: 8), // Reduced spacing
              _buildDiscountBadge(),
              const SizedBox(height: 8), // Reduced spacing
              Row(crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _buildPricingSection(),Spacer(),
                   _buildRoomsLeft(),
                ],
              ),
              const SizedBox(height: 8), // Reduced spacing
            
            ],
          ),
          
          // Keep counter controls at the bottom
          const SizedBox(height: 8), // Reduced spacing
          _buildCounterControls(),
        ],
      ),
    );
  }

  Widget _buildBenefitsTags() {
    List<Widget> tags = [];

    // Cancellation benefits
    if (widget.roomOption.cancellationBenefits?.code != null) {
      bool isRefundable = widget.roomOption.cancellationBenefits!.code != Code.NON_REFUNDABLE;
      
      tags.add(Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isRefundable 
              ? const Color(0xFFecfdf5) 
              : const Color(0xFFfef2f2),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isRefundable 
                ? const Color(0xFFd1fae5) 
                : const Color(0xFFfecaca),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isRefundable ? Icons.check_circle_rounded : Icons.info_rounded,
              size: 12,
              color: isRefundable ? const Color(0xFF059669) : const Color(0xFFdc2626),
            ),
            const SizedBox(width: 4),
            Text(
              isRefundable ? 'Refundable' : 'Non-refundable',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: isRefundable ? const Color(0xFF065f46) : const Color(0xFF991b1b),
              ),
            ),
          ],
        ),
      ));
    }

    // Meal benefits
    if (widget.roomOption.mealBenefits != null && widget.roomOption.mealBenefits!.isNotEmpty) {
      for (String benefit in widget.roomOption.mealBenefits!.take(2)) {
        tags.add(Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFeff6ff),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFdbeafe)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.restaurant_rounded,
                size: 12,
                color: Color(0xFF2563eb),
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  benefit,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1e40af),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ));
      }
    }

    // Other benefits
    if (widget.roomOption.otherBenefits != null && widget.roomOption.otherBenefits!.isNotEmpty) {
      for (String benefit in widget.roomOption.otherBenefits!.take(1)) {
        tags.add(Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFfefce8),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFfef3c7)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.star_rounded,
                size: 12,
                color: Color(0xFFd97706),
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  benefit,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF92400e),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ));
      }
    }

    return Wrap(
      spacing: 6,
      runSpacing: 6,
      children: tags,
    );
  }

  Widget _buildDiscountBadge() {
    if (widget.roomOption.fareDetail?.markupDiscountPercent == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFf59e0b), Color(0xFFd97706)],
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        '${widget.roomOption.fareDetail?.markupDiscountPercent}% OFF',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 11,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
      
          Text('₹${widget.roomOption.fareDetail?.totalPrice?.toInt() ?? 'N/A'}'
          ,
            style: const TextStyle(
              color: Color(0xFF64748b),
              fontSize: 13,
              decoration: TextDecoration.lineThrough,
              fontWeight: FontWeight.w500,
            ),
          ),
           if (widget.roomOption.fareDetail?.displayedBaseFare != null)
        Text(
            '₹${widget.roomOption.fareDetail!.displayedBaseFare}',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w800,
            color: Color(0xFF1a1a1a),
          ),
        ),
        if (widget.roomOption.fareDetail?.taxesAndFees != null)
          Text(
            '+ ₹${widget.roomOption.fareDetail!.taxesAndFees!.toInt()} ${'hotel.detail.taxes'.tr}',
            style: const TextStyle(
              fontSize: 11,
              color: Color(0xFF64748b),
              fontWeight: FontWeight.w500,
            ),
          ),
      Text(
          'hotel.detail.pernight'.tr,
          style: TextStyle(
            fontSize: 11,
            color: Color(0xFF64748b),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildRoomsLeft() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFFfef2f2),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        '${widget.room.roomsLeft ?? 0} rooms left',
        style: const TextStyle(
          fontSize: 11,
          color: Color(0xFFdc2626),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCounterControls() {
    final roomSelectionState = ref.watch(roomSelectionProvider);
    final roomSelectionNotifier = ref.read(roomSelectionProvider.notifier);

    final quantity = _getRoomOptionQuantity(roomSelectionState);
    final canAdd = _canAddRoom(roomSelectionState);

    if (quantity == 0) {
      return SizedBox(
        width: double.infinity,
        height: 40,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: canAdd ? AppColors.primary : const Color(0xFFf1f5f9),
            foregroundColor: canAdd ? Colors.white : const Color(0xFF64748b),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
            shadowColor: Colors.transparent,
          ),
          onPressed: canAdd ? () {
            _addRoomOption(roomSelectionNotifier);
          } : null,
          child: Text(
            canAdd ? 'Add Room' : 'Limit Reached',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              letterSpacing: -0.2,
            ),
          ),
        ),
      );
    } else {
      return Container(
        height: 40,
        decoration: BoxDecoration(
          color: const Color(0xFFf8fafc),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFe2e8f0), width: 1),
        ),
        child: Row(
          children: [
            // Minus button
            _buildCounterButton(
              icon: Icons.remove,
              onTap: () {
                final roomSelectionNotifier = ref.read(roomSelectionProvider.notifier);
                _removeRoomOption(roomSelectionNotifier);
              },
              isLeft: true,
            ),
            // Quantity display
            Expanded(
              flex: 2,
              child: Container(
                height: double.infinity,
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: Center(
                  child: Text(
                    quantity.toString(),
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Color(0xFF1e293b),
                    ),
                  ),
                ),
              ),
            ),
            // Plus button
            _buildCounterButton(
              icon: Icons.add,
              onTap: canAdd ? () {
                final roomSelectionNotifier = ref.read(roomSelectionProvider.notifier);
                _addRoomOption(roomSelectionNotifier);
              } : null,
              isRight: true,
              isEnabled: canAdd,
            ),
          ],
        ),
      );
    }
  }

  Widget _buildCounterButton({
    required IconData icon,
    required VoidCallback? onTap,
    bool isLeft = false,
    bool isRight = false,
    bool isEnabled = true,
  }) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.horizontal(
            left: isLeft ? const Radius.circular(8) : Radius.zero,
            right: isRight ? const Radius.circular(8) : Radius.zero,
          ),
          child: Container(
            height: double.infinity,
            child: Icon(
              icon,
              color: isEnabled 
                  ? const Color(0xFF475569) 
                  : const Color(0xFF94a3b8),
              size: 18,
            ),
          ),
        ),
      ),
    );
  }
}
