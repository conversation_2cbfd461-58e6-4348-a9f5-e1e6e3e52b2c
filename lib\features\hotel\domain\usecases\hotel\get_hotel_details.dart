﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/hotel_entity.dart';
import '../../repositories/hotel_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for getting hotel details by ID
class GetHotelDetails implements UseCase<HotelEntity, GetHotelDetailsParams> {
  final HotelRepository repository;

  GetHotelDetails(this.repository);

  @override
  Future<Either<Failure, HotelEntity>> call(GetHotelDetailsParams params) async {
    return await repository.getHotelDetails(params.hotelId);
  }
}

/// Parameters for GetHotelDetails use case
class GetHotelDetailsParams extends Equatable {
  final int hotelId;

  const GetHotelDetailsParams({required this.hotelId});

  @override
  List<Object> get props => [hotelId];
}

