import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/utils/safe_translation_helper.dart';

/// Base class for ConsumerStatefulWidget with built-in safety features
/// Provides safe methods for common operations that check widget lifecycle
abstract class SafeConsumerStatefulWidget extends ConsumerStatefulWidget {
  const SafeConsumerStatefulWidget({super.key});
}

/// Base state class with built-in safety features for widget lifecycle management
abstract class SafeConsumerState<T extends SafeConsumerStatefulWidget> 
    extends ConsumerState<T> {
  
  /// Safe setState that checks if widget is still mounted
  void safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    } else {
      debugPrint('Attempted setState on unmounted widget: ${widget.runtimeType}');
    }
  }
  
  /// Safe SnackBar display with mounted check
  void safeShowSnackBar(
    String message, {
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: duration,
          action: action,
        ),
      );
    }
  }
  
  /// Safe navigation with mounted check
  void safeNavigate(String routeName, {Object? arguments}) {
    if (mounted) {
      Navigator.pushNamed(context, routeName, arguments: arguments);
    }
  }
  
  /// Safe replacement navigation with mounted check
  void safeNavigateReplacement(String routeName, {Object? arguments}) {
    if (mounted) {
      Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
    }
  }
  
  /// Safe navigation pop with mounted check
  void safePop([Object? result]) {
    if (mounted) {
      Navigator.pop(context, result);
    }
  }
  
  /// Safe navigation push with mounted check
  Future<T?> safePush<T>(Route<T> route) async {
    if (mounted) {
      return Navigator.push<T>(context, route);
    }
    return null;
  }
  
  /// Safe translation methods with built-in context
  String safeTr(String key) {
    if (!mounted) return '** $key **';
    return key.tr(context);
  }
  
  String safeTrParams(String key, Map<String, String> params) {
    if (!mounted) return '** $key **';
    return key.trParams(context, params);
  }
  
  String safeTrPlural(String key, int count) {
    if (!mounted) return '** $key **';
    return key.trPlural(context, count);
  }
  
  /// Safe dialog display
  Future<T?> safeShowDialog<T>({
    required WidgetBuilder builder,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) async {
    if (mounted) {
      return showDialog<T>(
        context: context,
        builder: builder,
        barrierDismissible: barrierDismissible,
        barrierColor: barrierColor,
        barrierLabel: barrierLabel,
      );
    }
    return null;
  }
  
  /// Safe modal bottom sheet display
  Future<T?> safeShowModalBottomSheet<T>({
    required WidgetBuilder builder,
    bool isScrollControlled = false,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) async {
    if (mounted) {
      return showModalBottomSheet<T>(
        context: context,
        builder: builder,
        isScrollControlled: isScrollControlled,
        backgroundColor: backgroundColor,
        elevation: elevation,
        shape: shape,
      );
    }
    return null;
  }
  
  /// Safe focus management
  void safeFocusRequest(FocusNode focusNode) {
    if (mounted) {
      FocusScope.of(context).requestFocus(focusNode);
    }
  }
  
  void safeUnfocus() {
    if (mounted) {
      FocusScope.of(context).unfocus();
    }
  }
  
  /// Safe async operation wrapper
  Future<T?> safeAsyncOperation<T>(Future<T> Function() operation) async {
    try {
      final result = await operation();
      if (mounted) {
        return result;
      }
      return null;
    } catch (e) {
      if (mounted) {
        debugPrint('Async operation error in ${widget.runtimeType}: $e');
        safeShowSnackBar(
          'An error occurred: ${e.toString()}',
          backgroundColor: Colors.red,
        );
      }
      return null;
    }
  }
  
  /// Safe provider read with error handling
  T? safeProviderRead<T>(ProviderBase<T> provider) {
    try {
      if (mounted) {
        return ref.read(provider);
      }
      return null;
    } catch (e) {
      debugPrint('Provider read error in ${widget.runtimeType}: $e');
      return null;
    }
  }
  
  /// Safe provider watch with error handling
  T? safeProviderWatch<T>(ProviderListenable<T> provider) {
    try {
      if (mounted) {
        return ref.watch(provider);
      }
      return null;
    } catch (e) {
      debugPrint('Provider watch error in ${widget.runtimeType}: $e');
      return null;
    }
  }
}

/// Base class for regular StatefulWidget with safety features
abstract class SafeStatefulWidget extends StatefulWidget {
  const SafeStatefulWidget({super.key});
}

/// Base state class for regular StatefulWidget with safety features
abstract class SafeState<T extends SafeStatefulWidget> extends State<T> {
  
  /// Safe setState that checks if widget is still mounted
  void safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    } else {
      debugPrint('Attempted setState on unmounted widget: ${widget.runtimeType}');
    }
  }
  
  /// Safe SnackBar display with mounted check
  void safeShowSnackBar(
    String message, {
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: duration,
          action: action,
        ),
      );
    }
  }
  
  /// Safe navigation methods
  void safeNavigate(String routeName, {Object? arguments}) {
    if (mounted) {
      Navigator.pushNamed(context, routeName, arguments: arguments);
    }
  }
  
  void safePop([Object? result]) {
    if (mounted) {
      Navigator.pop(context, result);
    }
  }
  
  /// Safe translation methods
  String safeTr(String key) {
    if (!mounted) return '** $key **';
    return key.tr(context);
  }
  
  String safeTrParams(String key, Map<String, String> params) {
    if (!mounted) return '** $key **';
    return key.trParams(context, params);
  }
}
