import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/utils/location_helper.dart';
import 'package:kind_ali/features/search/data/models/search_cities.dart';
import 'package:kind_ali/features/search/data/models/autosuggest_models.dart';
import 'package:kind_ali/features/search/presentation/providers/search_cities_notifier.dart';

class SearchCitiesBottomSheet extends ConsumerStatefulWidget {
  final Function(String) onCitySelected;

  const SearchCitiesBottomSheet({
    super.key,
    required this.onCitySelected,
  });

  @override
  ConsumerState<SearchCitiesBottomSheet> createState() => _SearchCitiesBottomSheetState();
}

class _SearchCitiesBottomSheetState extends ConsumerState<SearchCitiesBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(searchCitiesProvider.notifier).loadSearchCitiesData();
      ref.read(searchCitiesProvider.notifier).getCurrentLocation();
      ref.read(searchCitiesProvider.notifier).loadRecentSearches();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final searchCitiesState = ref.watch(searchCitiesProvider);

    return Container(
          height: screenHeight * 0.85, // 85% of screen height
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(screenWidth * 0.05), // 5% of screen width
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: EdgeInsets.only(top: screenHeight * 0.01), // 1% of screen height
                width: screenWidth * 0.12, // 12% of screen width
                height: screenHeight * 0.005, // 0.5% of screen height
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(screenWidth * 0.01), // 1% of screen width
                ),
              ),

              // Header
              Padding(
                padding: EdgeInsets.all(screenWidth * 0.04), // 4% of screen width
                child: Row(
                  children: [
                    Text(
                      'Where are you going?',
                      style: TextStyle(
                        fontSize: screenHeight * 0.024, // 2.4% of screen height
                        fontWeight: FontWeight.bold,
                        color: AppColors.text,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.close,
                        size: screenHeight * 0.03, // 3% of screen height
                        color: AppColors.textLight,
                      ),
                    ),
                  ],
                ),
              ),

              // Search field
              Padding(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04), // 4% of screen width
                child: TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  onChanged: (value) => ref.read(searchCitiesProvider.notifier).searchWithAutosuggest(value),
                  decoration: InputDecoration(
                    hintText: 'Search destinations',
                    hintStyle: TextStyle(
                      color: AppColors.textLight,
                      fontSize: screenHeight * 0.018, // 1.8% of screen height
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: AppColors.primary,
                      size: screenHeight * 0.025, // 2.5% of screen height
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _searchController.clear();
                              ref.read(searchCitiesProvider.notifier).clearSearch();
                            },
                            icon: Icon(
                              Icons.clear,
                              color: AppColors.textLight,
                              size: screenHeight * 0.022, // 2.2% of screen height
                            ),
                          )
                        : null,
                    filled: true,
                    fillColor: AppColors.surface,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(screenWidth * 0.03), // 3% of screen width
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      vertical: screenHeight * 0.015, // 1.5% of screen height
                      horizontal: screenWidth * 0.04, // 4% of screen width
                    ),
                  ),
                ),
              ),

              SizedBox(height: screenHeight * 0.02), // 2% of screen height

              // Current location
              if (searchCitiesState.currentLocation != null)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04), // 4% of screen width
                  child: ListTile(
                    leading: Container(
                      padding: EdgeInsets.all(screenWidth * 0.02), // 2% of screen width
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(screenWidth * 0.02), // 2% of screen width
                      ),
                      child: Icon(
                        Icons.my_location,
                        color: AppColors.primary,
                        size: screenHeight * 0.025, // 2.5% of screen height
                      ),
                    ),
                    title: Text(
                      searchCitiesState.currentLocation!,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: screenHeight * 0.018, // 1.8% of screen height
                      ),
                    ),
                    subtitle: Text(
                      'Current location',
                      style: TextStyle(
                        color: AppColors.textLight,
                        fontSize: screenHeight * 0.014, // 1.4% of screen height
                      ),
                    ),
                    onTap: () {
                      final location = searchCitiesState.currentLocation!;
                      ref.read(searchCitiesProvider.notifier).addToRecentSearches(location);
                      widget.onCitySelected(location);
                      Navigator.pop(context);
                    },
                  ),
                ),

              // Location permission request (when location is not available)
              if (searchCitiesState.currentLocation == null && searchCitiesState.error == null)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
                  child: ListTile(
                    leading: Container(
                      padding: EdgeInsets.all(screenWidth * 0.02),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(screenWidth * 0.02),
                      ),
                      child: Icon(
                        Icons.location_disabled,
                        color: Colors.orange,
                        size: screenHeight * 0.025,
                      ),
                    ),
                    title: Text(
                      'Enable location',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: screenHeight * 0.018,
                      ),
                    ),
                    subtitle: Text(
                      'Get current location for better results',
                      style: TextStyle(
                        color: AppColors.textLight,
                        fontSize: screenHeight * 0.014,
                      ),
                    ),
                    onTap: () async {
                      // Request location permission
                      final result = await LocationHelper.getLocationWithErrorHandling(
                        context,
                        showErrorDialog: true,
                        onRetry: () {
                          ref.read(searchCitiesProvider.notifier).getCurrentLocation();
                        },
                      );

                      if (result.isSuccess) {
                        // Refresh the search cities data
                        ref.read(searchCitiesProvider.notifier).getCurrentLocation();
                      }
                    },
                  ),
                ),

              // Content
              Expanded(
                child: _buildContent(searchCitiesState, screenWidth, screenHeight),
              ),
            ],
          ),
        );
  }

  Widget _buildContent(SearchCitiesState searchCitiesState, double screenWidth, double screenHeight) {
    // Show autosuggest results when available and searching
    if (searchCitiesState.searchQuery.isNotEmpty && searchCitiesState.autosuggestResults.isNotEmpty) {
      return _buildAutosuggestResults(searchCitiesState, screenWidth, screenHeight);
    }

    // Show loading indicator for autosuggest
    if (searchCitiesState.isAutosuggestLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
          strokeWidth: screenWidth * 0.01, // 1% of screen width
        ),
      );
    }

    if (searchCitiesState.isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
          strokeWidth: screenWidth * 0.01, // 1% of screen width
        ),
      );
    }

    if (searchCitiesState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: screenHeight * 0.08, // 8% of screen height
              color: AppColors.error,
            ),
            SizedBox(height: screenHeight * 0.02), // 2% of screen height
            Text(
              searchCitiesState.error!,
              style: TextStyle(
                color: AppColors.error,
                fontSize: screenHeight * 0.018, // 1.8% of screen height
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: screenHeight * 0.02), // 2% of screen height
            ElevatedButton(
              onPressed: () {
                ref.read(searchCitiesProvider.notifier).clearError();
                ref.read(searchCitiesProvider.notifier).loadSearchCitiesData();
                // Also retry location if it was a location error
                if (searchCitiesState.error!.toLowerCase().contains('location')) {
                  ref.read(searchCitiesProvider.notifier).getCurrentLocation();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.06, // 6% of screen width
                  vertical: screenHeight * 0.015, // 1.5% of screen height
                ),
              ),
              child: Text(
                'Retry',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: screenHeight * 0.016, // 1.6% of screen height
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (searchCitiesState.searchQuery.isEmpty) {
      return _buildPopularContent(searchCitiesState, screenWidth, screenHeight);
    } else {
      return _buildSearchResults(searchCitiesState, screenWidth, screenHeight);
    }
  }

  Widget _buildPopularContent(SearchCitiesState searchCitiesState, double screenWidth, double screenHeight) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Searches
          if (searchCitiesState.recentSearches.isNotEmpty) ...[
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04), // 4% of screen width
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Recent Searches',
                    style: TextStyle(
                      fontSize: screenHeight * 0.02, // 2% of screen height
                      fontWeight: FontWeight.bold,
                      color: AppColors.text,
                    ),
                  ),
                  TextButton(
                    onPressed: () => ref.read(searchCitiesProvider.notifier).clearRecentSearches(),
                    child: Text(
                      'Clear',
                      style: TextStyle(
                        fontSize: screenHeight * 0.016, // 1.6% of screen height
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              height: screenHeight * 0.05, // 5% of screen height
              margin: EdgeInsets.only(bottom: screenHeight * 0.02), // 2% of screen height
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04), // 4% of screen width
                itemCount: searchCitiesState.recentSearches.length,
                separatorBuilder: (context, index) => SizedBox(width: screenWidth * 0.02), // 2% of screen width
                itemBuilder: (context, index) {
                  final search = searchCitiesState.recentSearches[index];
                  return _buildRecentSearchChip(search, screenWidth, screenHeight);
                },
              ),
            ),
          ],

          // Popular Places
          if (searchCitiesState.filteredPopularPlaces.isNotEmpty) ...[
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04), // 4% of screen width
              child: Text(
                'Popular Places',
                style: TextStyle(
                  fontSize: screenHeight * 0.02, // 2% of screen height
                  fontWeight: FontWeight.bold,
                  color: AppColors.text,
                ),
              ),
            ),
            ...searchCitiesState.filteredPopularPlaces.take(5).map((place) =>
              _buildPopularPlaceItem(place, screenWidth, screenHeight)),
          ],

          // Popular Cities
          if (searchCitiesState.popularCities.isNotEmpty) ...[
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04), // 4% of screen width
              child: Text(
                'Popular Cities',
                style: TextStyle(
                  fontSize: screenHeight * 0.02, // 2% of screen height
                  fontWeight: FontWeight.bold,
                  color: AppColors.text,
                ),
              ),
            ),
            ...searchCitiesState.popularCities.map((city) =>
              _buildCityItem(city, screenWidth, screenHeight)),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchResults(SearchCitiesState searchCitiesState, double screenWidth, double screenHeight) {
    final hasResults = searchCitiesState.filteredCities.isNotEmpty || searchCitiesState.filteredPopularPlaces.isNotEmpty;

    if (!hasResults) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: screenHeight * 0.08, // 8% of screen height
              color: AppColors.textLight,
            ),
            SizedBox(height: screenHeight * 0.02), // 2% of screen height
            Text(
              'No results found',
              style: TextStyle(
                fontSize: screenHeight * 0.02, // 2% of screen height
                fontWeight: FontWeight.w600,
                color: AppColors.text,
              ),
            ),
            SizedBox(height: screenHeight * 0.01), // 1% of screen height
            Text(
              'Try searching for a different destination',
              style: TextStyle(
                fontSize: screenHeight * 0.016, // 1.6% of screen height
                color: AppColors.textLight,
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cities results
          if (searchCitiesState.filteredCities.isNotEmpty) ...[
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04), // 4% of screen width
              child: Text(
                'Cities (${searchCitiesState.filteredCities.length})',
                style: TextStyle(
                  fontSize: screenHeight * 0.02, // 2% of screen height
                  fontWeight: FontWeight.bold,
                  color: AppColors.text,
                ),
              ),
            ),
            ...searchCitiesState.filteredCities.map((city) =>
              _buildCityItem(city, screenWidth, screenHeight)),
          ],

          // Popular places results
          if (searchCitiesState.filteredPopularPlaces.isNotEmpty) ...[
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04), // 4% of screen width
              child: Text(
                'Places (${searchCitiesState.filteredPopularPlaces.length})',
                style: TextStyle(
                  fontSize: screenHeight * 0.02, // 2% of screen height
                  fontWeight: FontWeight.bold,
                  color: AppColors.text,
                ),
              ),
            ),
            ...searchCitiesState.filteredPopularPlaces.map((place) =>
              _buildPopularPlaceItem(place, screenWidth, screenHeight)),
          ],
        ],
      ),
    );
  }

  Widget _buildCityItem(SearchCity city, double screenWidth, double screenHeight) {
    return ListTile(
      leading: Container(
        width: screenWidth * 0.12, // 12% of screen width
        height: screenWidth * 0.12, // Keep it square
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(screenWidth * 0.02), // 2% of screen width
        ),
        child: Center(
          child: Text(
            city.flagEmoji,
            style: TextStyle(fontSize: screenHeight * 0.025), // 2.5% of screen height
          ),
        ),
      ),
      title: Text(
        city.name,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: screenHeight * 0.018, // 1.8% of screen height
          color: AppColors.text,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${city.country} (${city.countryCode.toUpperCase()})',
            style: TextStyle(
              color: AppColors.textLight,
              fontSize: screenHeight * 0.014, // 1.4% of screen height
            ),
          ),
          Text(
            '${city.propertyCount} properties',
            style: TextStyle(
              color: AppColors.primary,
              fontSize: screenHeight * 0.013, // 1.3% of screen height
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: screenHeight * 0.02, // 2% of screen height
        color: AppColors.textLight,
      ),
      onTap: () {
        ref.read(searchCitiesProvider.notifier).addToRecentSearches(city.name);
        widget.onCitySelected(city.name);
        Navigator.pop(context);
      },
    );
  }

  Widget _buildRecentSearchChip(String search, double screenWidth, double screenHeight) {
    return InkWell(
      onTap: () {
        widget.onCitySelected(search);
        Navigator.pop(context);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04, // 4% of screen width
          vertical: screenHeight * 0.01, // 1% of screen height
        ),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(screenWidth * 0.06), // 6% of screen width
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.history,
              size: screenHeight * 0.018, // 1.8% of screen height
              color: AppColors.primary,
            ),
            SizedBox(width: screenWidth * 0.02), // 2% of screen width
            Text(
              search,
              style: TextStyle(
                fontSize: screenHeight * 0.016, // 1.6% of screen height
                color: AppColors.text,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularPlaceItem(PopularPlace place, double screenWidth, double screenHeight) {
    return ListTile(
      leading: Container(
        width: screenWidth * 0.12, // 12% of screen width
        height: screenWidth * 0.12, // Keep it square
        decoration: BoxDecoration(
          color: AppColors.accent.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(screenWidth * 0.02), // 2% of screen width
        ),
        child: Center(
          child: Text(
            place.flagEmoji,
            style: TextStyle(fontSize: screenHeight * 0.025), // 2.5% of screen height
          ),
        ),
      ),
      title: Text(
        place.name,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: screenHeight * 0.018, // 1.8% of screen height
          color: AppColors.text,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${place.city}, ${place.country}',
            style: TextStyle(
              color: AppColors.textLight,
              fontSize: screenHeight * 0.014, // 1.4% of screen height
            ),
          ),
          Text(
            '${place.propertyCount} properties',
            style: TextStyle(
              color: AppColors.accent,
              fontSize: screenHeight * 0.013, // 1.3% of screen height
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: screenHeight * 0.02, // 2% of screen height
        color: AppColors.textLight,
      ),
      onTap: () {
        ref.read(searchCitiesProvider.notifier).addToRecentSearches(place.city);
        widget.onCitySelected(place.city);
        Navigator.pop(context);
      },
    );
  }

  /// Build autosuggest results list
  Widget _buildAutosuggestResults(SearchCitiesState searchCitiesState, double screenWidth, double screenHeight) {
    return ListView.builder(
      itemCount: searchCitiesState.autosuggestResults.length,
      itemBuilder: (context, index) {
        final suggestion = searchCitiesState.autosuggestResults[index];
        return _buildAutosuggestItem(suggestion, screenWidth, screenHeight);
      },
    );
  }

  /// Build individual autosuggest item
  Widget _buildAutosuggestItem(AutosuggestItem suggestion, double screenWidth, double screenHeight) {
    return ListTile(
      leading: Container(
        width: screenWidth * 0.12,
        height: screenWidth * 0.12,
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(screenWidth * 0.02),
        ),
        child: Icon(
          _getIconForType(suggestion.type),
          color: AppColors.primary,
          size: screenHeight * 0.025,
        ),
      ),
      title: Text(
        suggestion.name,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: screenHeight * 0.018,
          color: AppColors.text,
        ),
      ),
      subtitle: Text(
        _buildSubtitle(suggestion),
        style: TextStyle(
          color: AppColors.textLight,
          fontSize: screenHeight * 0.014,
        ),
      ),
      trailing: _buildTrailingWidget(suggestion, screenHeight),
      onTap: () {
        ref.read(searchCitiesProvider.notifier).addToRecentSearches(suggestion.name);
        widget.onCitySelected(suggestion.name);
        Navigator.pop(context);
      },
    );
  }

  /// Get icon for suggestion type
  IconData _getIconForType(String type) {
    switch (type.toLowerCase()) {
      case 'hotel':
        return Icons.hotel;
      case 'city':
        return Icons.location_city;
      case 'neighborhood':
        return Icons.location_on;
      case 'landmark':
        return Icons.place;
      case 'airport':
        return Icons.flight;
      default:
        return Icons.location_city;
    }
  }

  /// Build trailing widget for autosuggest item
  Widget? _buildTrailingWidget(AutosuggestItem suggestion, double screenHeight) {
    if (suggestion.type.toLowerCase() == 'hotel') {
      return Icon(
        Icons.arrow_forward_ios,
        size: screenHeight * 0.02,
        color: AppColors.textLight,
      );
    } else if (suggestion.propertyCount != null) {
      return Text(
        '${suggestion.propertyCount} properties',
        style: TextStyle(
          color: AppColors.primary,
          fontSize: screenHeight * 0.012,
          fontWeight: FontWeight.w500,
        ),
      );
    }
    return Icon(
      Icons.arrow_forward_ios,
      size: screenHeight * 0.02,
      color: AppColors.textLight,
    );
  }

  /// Build subtitle for autosuggest item
  String _buildSubtitle(AutosuggestItem suggestion) {
    // For your API, description contains the fullName which is more descriptive
    if (suggestion.description != null &&
        suggestion.description!.isNotEmpty &&
        suggestion.description != suggestion.name) {
      return suggestion.description!;
    }

    // Fallback to country if no description
    if (suggestion.country != null && suggestion.country!.isNotEmpty) {
      return suggestion.country!;
    }

    return '';
  }
}