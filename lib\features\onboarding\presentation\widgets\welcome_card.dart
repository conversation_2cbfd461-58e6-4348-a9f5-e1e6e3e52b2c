import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/shared/widgets/custombutton_widget.dart';

class WelcomeCard extends StatefulWidget {
  final VoidCallback onContinue;

  const WelcomeCard({super.key, required this.onContinue});

  @override
  State<WelcomeCard> createState() => _WelcomeCardState();
}

class _WelcomeCardState extends State<WelcomeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.4, 1.0, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = size.width * 0.05;
    final imageSize = size.height * 0.14;
    final iconSize = size.height * 0.045;
    final titleFontSize = size.height * 0.03;
    final bodyFontSize = size.height * 0.018;
    final smallFontSize = size.height * 0.014;
    final spacingSmall = size.height * 0.015;
    final spacingMedium = size.height * 0.03;
    final spacingLarge = size.height * 0.05;
    final buttonHeight = size.height * 0.07;
    final borderRadius = size.width * 0.04;

    return Padding(
      padding: EdgeInsets.all(padding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Logo
          AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: imageSize,
                  height: imageSize,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(imageSize / 2),
                  ),
                  child: Center(
                    child: Image.asset(
                      AppImages.logo,
                      width: imageSize * 0.66,
                      height: imageSize * 0.66,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              );
            },
          ),

          SizedBox(height: spacingLarge),

          // Text
          FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                Text(
                  '👋 Welcome to KindAli!',
                  style: AppTextStyles.headline1.copyWith(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppColors.text,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: spacingSmall),
                Text(
                  'Let\'s set up your profile so you can start exploring amazing hotels and experiences.',
                  style: AppTextStyles.bodyText1.copyWith(
                    fontSize: bodyFontSize,
                    color: AppColors.textLight,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          SizedBox(height: spacingLarge),

          // Features
          SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  _buildFeatureItem(
                    icon: Icons.hotel,
                    title: 'Discover Hotels',
                    description: 'Find perfect stays worldwide',
                    iconSize: iconSize,
                    titleFontSize: bodyFontSize,
                    descriptionFontSize: smallFontSize,
                  ),
                  SizedBox(height: spacingSmall),
                  _buildFeatureItem(
                    icon: Icons.favorite,
                    title: 'Save Favorites',
                    description: 'Keep track of places you love',
                    iconSize: iconSize,
                    titleFontSize: bodyFontSize,
                    descriptionFontSize: smallFontSize,
                  ),
                  SizedBox(height: spacingSmall),
                  _buildFeatureItem(
                    icon: Icons.star,
                    title: 'Earn Rewards',
                    description: 'Get points with every booking',
                    iconSize: iconSize,
                    titleFontSize: bodyFontSize,
                    descriptionFontSize: smallFontSize,
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: spacingLarge),

          // Button
          SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: CustombuttonWidget(
                text: 'Let\'s Get Started! 🚀',
                backgroundColor: AppColors.primary,
                textColor: Colors.white,
                borderRadius: borderRadius,
                height: buttonHeight,
                isFullWidth: true,
                onPressed: widget.onContinue,
                textStyle: AppTextStyles.button.copyWith(
                  fontSize: bodyFontSize,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          SizedBox(height: spacingSmall * 1.2),

          // Skip
          FadeTransition(
            opacity: _fadeAnimation,
            child: TextButton(
              onPressed: widget.onContinue,
              child: Text(
                'This will only take a minute',
                style: TextStyle(
                  color: AppColors.textLight,
                  fontSize: smallFontSize,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required double iconSize,
    required double titleFontSize,
    required double descriptionFontSize,
  }) {
    return Row(
      children: [
        Container(
          width: iconSize,
          height: iconSize,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(iconSize * 0.25),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: iconSize * 0.5,
          ),
        ),
        SizedBox(width: iconSize * 0.375),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyText1.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.text,
                  fontSize: titleFontSize,
                ),
              ),
              Text(
                description,
                style: AppTextStyles.bodyText2.copyWith(
                  color: AppColors.textLight,
                  fontSize: descriptionFontSize,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
