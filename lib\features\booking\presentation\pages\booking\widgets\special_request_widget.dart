import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';

class SpecialRequestWidget extends StatefulWidget {
  final Function(String) onRequestSubmitted;
  
  const SpecialRequestWidget({
    Key? key,
    required this.onRequestSubmitted,
  }) : super(key: key);

  @override
  State<SpecialRequestWidget> createState() => _SpecialRequestWidgetState();
}

class _SpecialRequestWidgetState extends State<SpecialRequestWidget> {
  final TextEditingController _requestController = TextEditingController();
  bool _hasRequest = false;
  
  @override
  void dispose() {
    _requestController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Special Request',
            style: AppTextStyles.headline3.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // Description
          Text(
            'booking.sprqnote'.tr,
            style: TextStyle(
              color: Colors.grey.shade700,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          
          // Request button or text field
          if (!_hasRequest)
            _buildAddRequestButton()
          else
            _buildRequestTextField(),
        ],
      ),
    );
  }
  
  Widget _buildAddRequestButton() {
    return InkWell(
      onTap: () {
        setState(() {
          _hasRequest = true;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              color: AppColors.primary,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              'Add Request',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRequestTextField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _requestController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Enter your special request here...',
            hintStyle: TextStyle(
              color: Colors.grey.shade500,
              fontSize: 14,
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
            ),
            contentPadding: const EdgeInsets.all(12),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                setState(() {
                  _requestController.clear();
                  _hasRequest = false;
                });
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade700,
              ),
              child: const Text('Cancel'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                if (_requestController.text.trim().isNotEmpty) {
                  widget.onRequestSubmitted(_requestController.text.trim());
                  setState(() {
                    _hasRequest = false;
                  });
                  
                  // Show confirmation
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Your request has been submitted'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Submit'),
            ),
          ],
        ),
      ],
    );
  }
}
