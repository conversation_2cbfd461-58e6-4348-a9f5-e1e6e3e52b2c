/// Home screen state management using Riverpod StateNotifier
library;

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:kind_ali/features/search/data/models/recent_search.dart';
import 'package:kind_ali/features/home/<USER>/models/tourist_place_model.dart';

/// Room data class
class RoomData {
  final int adults;
  final int children;

  const RoomData({
    required this.adults,
    required this.children,
  });

  RoomData copyWith({
    int? adults,
    int? children,
  }) {
    return RoomData(
      adults: adults ?? this.adults,
      children: children ?? this.children,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RoomData &&
        other.adults == adults &&
        other.children == children;
  }

  @override
  int get hashCode => Object.hash(adults, children);

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'adults': adults,
      'children': children,
    };
  }

  /// Create from JSON
  factory RoomData.fromJson(Map<String, dynamic> json) {
    return RoomData(
      adults: json['adults'] ?? 2,
      children: json['children'] ?? 0,
    );
  }
}

/// Home state class
class HomeState {
  final TextEditingController destinationController;
  final List<RoomData> rooms;
  final bool isLoading;
  final List<TouristPlaces> places;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;
  final int? guestCount;
  final int roomCount;
  final bool isAppBarVisible;
  final double appBarHeight;
  final String? currentLocation;
  final List<RecentSearch> recentSearches;

  const HomeState({
    required this.destinationController,
    this.rooms = const [RoomData(adults: 2, children: 0)],
    this.isLoading = true,
    this.places = const [],
    this.checkInDate,
    this.checkOutDate,
    this.guestCount,
    this.roomCount = 1,
    this.isAppBarVisible = true,
    this.appBarHeight = 0,
    this.currentLocation,
    this.recentSearches = const [],
  });

  HomeState copyWith({
    TextEditingController? destinationController,
    List<RoomData>? rooms,
    bool? isLoading,
    List<TouristPlaces>? places,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? guestCount,
    int? roomCount,
    bool? isAppBarVisible,
    double? appBarHeight,
    String? currentLocation,
    List<RecentSearch>? recentSearches,
  }) {
    return HomeState(
      destinationController: destinationController ?? this.destinationController,
      rooms: rooms ?? this.rooms,
      isLoading: isLoading ?? this.isLoading,
      places: places ?? this.places,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      guestCount: guestCount ?? this.guestCount,
      roomCount: roomCount ?? this.roomCount,
      isAppBarVisible: isAppBarVisible ?? this.isAppBarVisible,
      appBarHeight: appBarHeight ?? this.appBarHeight,
      currentLocation: currentLocation ?? this.currentLocation,
      recentSearches: recentSearches ?? this.recentSearches,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HomeState &&
        other.destinationController == destinationController &&
        other.rooms == rooms &&
        other.isLoading == isLoading &&
        other.places == places &&
        other.checkInDate == checkInDate &&
        other.checkOutDate == checkOutDate &&
        other.guestCount == guestCount &&
        other.roomCount == roomCount &&
        other.isAppBarVisible == isAppBarVisible &&
        other.appBarHeight == appBarHeight &&
        other.currentLocation == currentLocation &&
        other.recentSearches == recentSearches;
  }

  @override
  int get hashCode => Object.hash(
        destinationController,
        rooms,
        isLoading,
        places,
        checkInDate,
        checkOutDate,
        guestCount,
        roomCount,
        isAppBarVisible,
        appBarHeight,
        currentLocation,
        recentSearches,
      );
}

/// Home StateNotifier
class HomeNotifier extends StateNotifier<HomeState> {
  static const String _recentSearchesKey = 'home_recent_searches';

  HomeNotifier()
      : super(HomeState(
          destinationController: TextEditingController(),
        )) {
    _initializeDefaults();
    loadUsers();
    _loadRecentSearches();
  }

  @override
  void dispose() {
    state.destinationController.dispose();
    super.dispose();
  }

  /// Initialize default values
  void _initializeDefaults() {
    final now = DateTime.now();
    state = state.copyWith(
      checkInDate: now,
      checkOutDate: now.add(const Duration(days: 3)),
      guestCount: 1,
    );
  }

  /// Load tourist places data
  Future<void> loadUsers() async {
    try {
      state = state.copyWith(isLoading: true);

      final String response = await rootBundle
          .loadString('assets/tourist places.json')
          .catchError((error) {
        return '''[
          {
            "country": "United States",
            "region": "North America",
            "places": ["New York", "Los Angeles", "Chicago", "Miami", "Las Vegas"]
          },
          {
            "country": "France",
            "region": "Europe",
            "places": ["Paris", "Nice", "Lyon", "Marseille", "Bordeaux"]
          },
          {
            "country": "Japan",
            "region": "Asia",
            "places": ["Tokyo", "Kyoto", "Osaka", "Hiroshima", "Sapporo"]
          }
        ]''';
      });

      final data = json.decode(response) as List;
      final places = data.map((json) => TouristPlaces.fromJson(json)).toList();
      
      state = state.copyWith(
        places: places,
        isLoading: false,
      );
    } catch (e) {
      // Handle any errors
      state = state.copyWith(
        places: [],
        isLoading: false,
      );
    }
  }

  /// Update check-in date
  void setCheckInDate(DateTime? date) {
    var newCheckOutDate = state.checkOutDate;
    
    // If check-out date is before check-in date, adjust it
    if (newCheckOutDate != null && date != null && newCheckOutDate.isBefore(date)) {
      newCheckOutDate = date.add(const Duration(days: 1));
    }
    
    state = state.copyWith(
      checkInDate: date,
      checkOutDate: newCheckOutDate,
    );
  }

  /// Update check-out date
  void setCheckOutDate(DateTime? date) {
    state = state.copyWith(checkOutDate: date);
  }

  /// Update guest count
  void setGuestCount(int count) {
    state = state.copyWith(guestCount: count);
  }

  /// Check if search is valid
  bool isSearchValid() {
    return state.checkInDate != null && state.checkOutDate != null;
  }

  /// Update destination from popular destinations
  void setDestination(String destination) {
    state.destinationController.text = destination;
    // Note: StateNotifier doesn't automatically notify for TextEditingController changes
    // We need to trigger a state update
    state = state.copyWith();
  }

  /// Load recent searches from SharedPreferences
  Future<void> _loadRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recentSearchesJson = prefs.getString(_recentSearchesKey);
      
      if (recentSearchesJson != null) {
        final List<dynamic> jsonList = json.decode(recentSearchesJson);
        final recentSearches = jsonList
            .map((json) => RecentSearch.fromJson(json))
            .toList();
        
        state = state.copyWith(recentSearches: recentSearches);
      }
    } catch (e) {
      // Handle error silently
    }
  }

  /// Save recent searches to SharedPreferences
  Future<void> _saveRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = state.recentSearches.map((search) => search.toJson()).toList();
      final recentSearchesJson = json.encode(jsonList);
      await prefs.setString(_recentSearchesKey, recentSearchesJson);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Add recent search
  Future<void> addRecentSearch(RecentSearch search) async {
    final updatedSearches = [search, ...state.recentSearches];
    
    // Keep only the last 10 searches
    if (updatedSearches.length > 10) {
      updatedSearches.removeRange(10, updatedSearches.length);
    }
    
    state = state.copyWith(recentSearches: updatedSearches);
    await _saveRecentSearches();
  }

  /// Clear recent searches
  Future<void> clearRecentSearches() async {
    state = state.copyWith(recentSearches: []);
    await _saveRecentSearches();
  }

  /// Apply recent search to current search form
  void applyRecentSearch(RecentSearch search) {
    state.destinationController.text = search.destination;
    state = state.copyWith(
      checkInDate: search.checkInDate,
      checkOutDate: search.checkOutDate,
      rooms: search.rooms,
    );
  }

  /// Update app bar visibility
  void setAppBarVisibility(bool isVisible) {
    state = state.copyWith(isAppBarVisible: isVisible);
  }

  /// Update app bar height
  void setAppBarHeight(double height) {
    state = state.copyWith(appBarHeight: height);
  }

  /// Update current location
  void setCurrentLocation(String? location) {
    state = state.copyWith(currentLocation: location);
  }

  /// Update room count
  void setRoomCount(int count) {
    state = state.copyWith(roomCount: count);
  }

  /// Update rooms data
  void setRooms(List<RoomData> rooms) {
    state = state.copyWith(rooms: rooms);
  }
}

/// Home provider
final homeProvider = StateNotifierProvider<HomeNotifier, HomeState>(
  (ref) => HomeNotifier(),
);
