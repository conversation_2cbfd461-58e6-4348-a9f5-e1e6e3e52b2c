﻿import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kind_ali/core/error/failures.dart';
import '../../entities/user_entity.dart';
import '../../repositories/auth_repository.dart';
import 'package:kind_ali/core/utils/typedef.dart';
import 'package:kind_ali/core/domain/usecases/base/usecase.dart';

/// Use case for phone registration
class RegisterWithPhone implements UseCase<AuthEntity, RegisterWithPhoneParams> {
  final AuthRepository repository;

  RegisterWithPhone(this.repository);

  @override
  Future<Either<Failure, AuthEntity>> call(RegisterWithPhoneParams params) async {
    return await repository.registerWithPhone(
      firstName: params.firstName,
      lastName: params.lastName,
      phone: params.phone,
    );
  }
}

/// Parameters for RegisterWithPhone use case
class RegisterWithPhoneParams extends Equatable {
  final String firstName;
  final String lastName;
  final String phone;

  const RegisterWithPhoneParams({
    required this.firstName,
    required this.lastName,
    required this.phone,
  });

  @override
  List<Object> get props => [firstName, lastName, phone];
}

